# Environment Configuration for BFF

# Node.js Environment
NODE_ENV=production

# Logging Level (debug, info, warn, error)
LOG_LEVEL=info

# CORS Settings (comma-separated origins)
ALLOWED_ORIGINS=*

# Health Check Settings for Vercel
VERCEL_HEALTH_CHECK_TIMEOUT=15000
VERCEL_FORCE_HEALTH_CHECK=true

# Backend Timeout Overrides (in milliseconds)
BACKEND_TIMEOUT_OVERRIDE=10000
HEALTH_CHECK_TIMEOUT_OVERRIDE=15000

# Circuit Breaker Settings
CIRCUIT_BREAKER_FAILURE_THRESHOLD=3
CIRCUIT_BREAKER_RESET_TIMEOUT=30000

# Cache Settings
CACHE_ENABLED=true
CACHE_TTL=300000

# Rate Limiting
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW_MS=60000
