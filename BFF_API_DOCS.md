
# BFF API Documentation

This document outlines the API endpoints for the BFF (Backend for Frontend) service, based on the `api.js` file.

**Base URL:** The base URL for all API calls is retrieved dynamically from `/config.json` or `Obtain through a specific URL`.

**Authentication:** Most API calls require an authentication token to be sent in the `Authorization` header.

---

### User APIs

These APIs require an authenticated user session.

| Function Name          | HTTP Method | Endpoint                                                | Parameters                               | Description                |
| ---------------------- | ----------- | ------------------------------------------------------- | ---------------------------------------- | -------------------------- |
| `getSubscribe`         | `GET`       | `/api/v1/user/getSubscribe`                             |                                          | Get user subscription data.|
| `getUserInfo`          | `GET`       | `/api/v1/user/info`                                     |                                          | Get user info.             |
| `getServerList`        | `GET`       | `/api/v1/user/server/fetch`                             |                                          | Get server list.           |
| `getTrafficLog`        | `GET`       | `/api/v1/user/stat/getTrafficLog`                       |                                          | Get traffic log.           |
| `getOrders`            | `GET`       | `/api/v1/user/order/fetch`                              |                                          | Get orders.                |
| `getPlans`             | `GET`       | `/api/v1/user/plan/fetch`                               |                                          | Get plans.                 |
| `resetSubscribeUrl`    | `POST`      | `/api/v1/user/resetSecurity`                            |                                          | Reset subscription URL.    |
| `getNotices`           | `GET`       | `/api/v1/user/notice/fetch`                             |                                          | Get notices/announcements. |
| `changePassword`       | `POST`      | `/api/v1/user/changePassword`                           | `old_password`, `new_password`           | Change user password.      |
| `resetSubscription`    | `GET`       | `/api/v1/user/resetSecurity`                            |                                          | Reset subscription URL.    |
| `getKnowledgeList`     | `GET`       | `/api/v1/user/knowledge/fetch?language=zh-CN`           |                                          | Get knowledge base list.   |
| `getKnowledgeDetail`   | `GET`       | `/api/v1/user/knowledge/fetch?id={id}&language=zh-CN`   | `id`                                     | Get knowledge base detail. |
| `getPlanDetail`        | `GET`       | `/api/v1/user/plan/fetch?id={planId}`                   | `planId`                                 | Get plan detail.           |
| `checkCoupon`          | `POST`      | `/api/v1/user/coupon/check`                             | `plan_id`, `code`                        | Check coupon.              |
| `createOrder`          | `POST`      | `/api/v1/user/order/save`                               | `plan_id`, `period`, `coupon_code` (opt) | Create order.              |
| `getPaymentMethods`    | `GET`       | `/api/v1/user/order/getPaymentMethod`                   |                                          | Get payment methods.       |
| `payOrder`             | `POST`      | `/api/v1/user/order/checkout`                           | `trade_no`, `method`                     | Pay order.                 |
| `getOrderDetail`       | `GET`       | `/api/v1/user/order/details?trade_no={tradeNo}`         | `tradeNo`                                | Get order detail.          |
| `checkOrderStatus`     | `GET`       | `/api/v1/user/order/check?trade_no={tradeNo}`           | `tradeNo`                                | Check order status.        |
| `cancelOrder`          | `POST`      | `/api/v1/user/order/cancel`                             | `trade_no`                               | Cancel order.              |

---

### Guest APIs

These APIs do not require an authentication token.

| Function Name       | HTTP Method | Endpoint                                  | Parameters                                     | Description              |
| ------------------- | ----------- | ----------------------------------------- | ---------------------------------------------- | ------------------------ |
| `getGuestConfig`    | `GET`       | `/api/v1/guest/comm/config`               |                                                | Get guest configuration. |
| `sendEmailVerify`   | `POST`      | `/api/v1/passport/comm/sendEmailVerify`   | `email`                                        | Send email verification. |
| `register`          | `POST`      | `/api/v1/passport/auth/register`          | `email`, `password`, `invite_code`, `email_code` | Register a new user.     |
| `forgotPassword`    | `POST`      | `/api/v1/passport/auth/forget`            | `email`, `password`, `email_code`              | Forgot password.         |
