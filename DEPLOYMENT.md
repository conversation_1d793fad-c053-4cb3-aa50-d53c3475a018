# Multi-Platform Deployment Guide

This guide covers deploying the High-Speed BFF on different serverless platforms.

## Platform Comparison

| Feature | Vercel | Netlify | Cloudflare Workers |
|---------|--------|---------|-------------------|
| **Ease of Setup** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Performance** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Global Edge** | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Cost (Free Tier)** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Node.js Support** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Cold Start** | ~100ms | ~200ms | ~5ms |

## Vercel Deployment

### Prerequisites
- Git repository (GitHub, GitLab, Bitbucket)
- Vercel account

### Steps
1. **Connect Repository**
   ```bash
   # Push to Git repository
   git add .
   git commit -m "Add multi-platform support"
   git push origin main
   ```

2. **Import to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your repository
   - Vercel auto-detects `vercel.json`

3. **Environment Variables** (Optional)
   ```
   HEALTH_CHECK_TIMEOUT_OVERRIDE=15000
   LOG_LEVEL=info
   NODE_ENV=production
   ALLOWED_ORIGINS=*
   ```

4. **Deploy**
   - Vercel deploys automatically on push
   - Or use CLI: `npx vercel --prod`

### Vercel Configuration
The `vercel.json` file handles:
- Frontend build (`vite build`)
- API routes to serverless functions
- Static file serving

## Netlify Deployment

### Prerequisites
- Git repository
- Netlify account
- `serverless-http` dependency

### Steps
1. **Install Dependencies**
   ```bash
   npm install serverless-http --save-dev
   ```

2. **Connect Repository**
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your repository

3. **Build Settings**
   - Build command: `npm run build:netlify`
   - Publish directory: `dist`
   - Functions directory: `netlify/functions`

4. **Environment Variables**
   ```
   HEALTH_CHECK_TIMEOUT_OVERRIDE=15000
   LOG_LEVEL=info
   NODE_ENV=production
   NETLIFY=true
   ```

5. **Deploy**
   ```bash
   # Using Netlify CLI
   npm install -g netlify-cli
   netlify login
   netlify deploy --prod
   
   # Or use npm script
   npm run deploy:netlify
   ```

### Netlify Configuration
The `netlify.toml` file handles:
- Build settings
- Function redirects
- Headers and security
- Environment variables

## Cloudflare Workers Deployment

### Prerequisites
- Cloudflare account
- Wrangler CLI
- Workers subscription (for production)

### Steps
1. **Install Wrangler**
   ```bash
   npm install -g wrangler
   # or
   npm install wrangler --save-dev
   ```

2. **Login to Cloudflare**
   ```bash
   wrangler login
   ```

3. **Configure wrangler.toml**
   ```toml
   name = "your-bff-worker"
   main = "workers/simple-adapter.js"
   compatibility_date = "2024-01-01"
   
   [vars]
   NODE_ENV = "production"
   CLOUDFLARE_WORKERS = "true"
   ```

4. **Deploy**
   ```bash
   # Build and deploy
   npm run build:workers
   wrangler publish
   
   # Or use npm script
   npm run deploy:workers
   ```

### Cloudflare Limitations
- Simplified adapter (no full Express.js)
- 10ms CPU time limit (free tier)
- Limited Node.js API support
- No file system access

## Environment Variables

### Common Variables
```bash
# Logging
LOG_LEVEL=info                    # debug, info, warn, error
NODE_ENV=production              # development, production

# Health Checks
HEALTH_CHECK_TIMEOUT_OVERRIDE=15000  # Timeout in milliseconds
BACKEND_TIMEOUT_OVERRIDE=10000       # Backend request timeout

# Security
ALLOWED_ORIGINS=*                    # CORS origins (comma-separated)

# Circuit Breaker
CIRCUIT_BREAKER_FAILURE_THRESHOLD=3  # Failures before opening
CIRCUIT_BREAKER_RESET_TIMEOUT=30000  # Reset timeout in ms
```

### Platform-Specific Variables
```bash
# Vercel
VERCEL=true                      # Auto-set by Vercel

# Netlify
NETLIFY=true                     # Auto-set by Netlify

# Cloudflare Workers
CLOUDFLARE_WORKERS=true          # Auto-set by Workers
CF_PAGES=true                    # Auto-set by Pages
```

## Testing Deployments

### Quick Health Check
```bash
# Test health endpoint
curl https://your-deployment.com/health

# Test status endpoint
curl https://your-deployment.com/status
```

### Comprehensive Testing
```bash
# Test all platforms
npm run test:multi-platform \
  vercel=https://your-project.vercel.app \
  netlify=https://your-project.netlify.app \
  cloudflare=https://your-worker.workers.dev
```

### Performance Testing
```bash
# Test response times
time curl https://your-deployment.com/health

# Load testing (using Apache Bench)
ab -n 100 -c 10 https://your-deployment.com/health
```

## Troubleshooting

### Common Issues

1. **"No healthy backends available"**
   - Check backend URLs are accessible from the internet
   - Verify health check timeouts
   - Check environment variables

2. **Function Timeout**
   - Increase timeout settings
   - Optimize health check logic
   - Use environment variable overrides

3. **CORS Issues**
   - Set `ALLOWED_ORIGINS` environment variable
   - Check platform-specific CORS settings

4. **Cold Start Performance**
   - Vercel: ~100ms (acceptable)
   - Netlify: ~200ms (acceptable)
   - Cloudflare: ~5ms (excellent)

### Platform-Specific Issues

**Vercel:**
- Function timeout: 10s (Hobby), 60s (Pro)
- Memory limit: 1024MB (Hobby), 3008MB (Pro)

**Netlify:**
- Function timeout: 10s (free), 26s (paid)
- Memory limit: 1024MB

**Cloudflare Workers:**
- CPU time: 10ms (free), 50ms (paid)
- Memory limit: 128MB
- Limited Node.js APIs

## Best Practices

1. **Environment Detection**
   - The BFF automatically detects the platform
   - Adjusts timeouts and behavior accordingly

2. **Health Check Strategy**
   - Serverless: On-demand health checks
   - Traditional: Scheduled health checks

3. **Error Handling**
   - Platform-specific error responses
   - Detailed logging for debugging

4. **Performance Optimization**
   - Use appropriate timeouts for each platform
   - Implement caching strategies
   - Monitor cold start times

## Monitoring

### Built-in Endpoints
- `/health` - Health status and backend info
- `/metrics` - Performance metrics
- `/status` - Basic service status

### External Monitoring
- Set up uptime monitoring (Pingdom, UptimeRobot)
- Monitor response times
- Set up alerts for health check failures

### Logs
- Vercel: Function logs in dashboard
- Netlify: Function logs in dashboard
- Cloudflare: Real-time logs in dashboard
