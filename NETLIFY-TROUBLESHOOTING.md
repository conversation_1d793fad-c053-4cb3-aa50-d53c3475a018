# Netlify Deployment Troubleshooting

## Common Netlify Build Issues

### 1. npm Installation Errors

**Error**: `npm error enoent Could not read package.json`

**Solutions**:
1. **Check Node.js version**:
   - Ensure `.nvmrc` file exists with `18.20.8`
   - Update `netlify.toml` with correct Node version

2. **Clear build cache**:
   - Go to Netlify dashboard
   - Site Settings > Build & Deploy > Environment
   - Click "Clear cache and retry deploy"

3. **Use npm ci instead of npm install**:
   ```toml
   [build]
   command = "npm ci && npm run build"
   ```

4. **Check package.json dependencies**:
   - Ensure `serverless-http` is in `dependencies`, not `devDependencies`
   - Verify all version numbers are valid

### 2. Function Deployment Issues

**Error**: Function fails to load dependencies

**Solutions**:
1. **Check function file**:
   ```javascript
   // netlify/functions/api.js should handle errors gracefully
   try {
     const serverless = require('serverless-http');
     const app = require('../../api/server');
   } catch (error) {
     console.error('Failed to load dependencies:', error);
   }
   ```

2. **Use fallback health endpoint**:
   - Access `/health-simple` instead of `/health`
   - This uses a simplified function without full BFF dependencies

3. **Check function logs**:
   - Netlify dashboard > Functions tab
   - Click on function name to view logs

### 3. Build Command Issues

**Current build command**: `npm ci && npm run build`

**Alternative commands to try**:
```toml
# Option 1: Basic build
[build]
command = "npm install && npm run build"

# Option 2: Skip optional dependencies
[build]
command = "npm install --no-optional && npm run build"

# Option 3: Use yarn instead
[build]
command = "yarn install && yarn build"
```

### 4. Environment Variables

**Required variables**:
```
NODE_ENV=production
NETLIFY=true
HEALTH_CHECK_TIMEOUT_OVERRIDE=15000
LOG_LEVEL=info
```

**How to set**:
1. Go to Netlify dashboard
2. Site Settings > Environment Variables
3. Add each variable

### 5. Function Timeout Issues

**Netlify limits**:
- Free tier: 10 seconds
- Pro tier: 26 seconds

**Solutions**:
1. **Optimize health checks**:
   ```javascript
   // Reduce timeout in Netlify environment
   const timeout = process.env.NETLIFY ? 8000 : 15000;
   ```

2. **Use simplified health check**:
   - Access `/health-simple` for basic status
   - Full health check at `/health` when needed

## Testing Netlify Deployment

### 1. Local Testing
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Test functions locally
netlify dev

# Test specific function
netlify functions:invoke api --payload '{"httpMethod":"GET","path":"/health"}'
```

### 2. Deploy Testing
```bash
# Deploy to preview
netlify deploy

# Deploy to production
netlify deploy --prod

# Test deployment
npm run test:deployment https://your-site.netlify.app
```

### 3. Function Testing
```bash
# Test main API function
curl https://your-site.netlify.app/health

# Test fallback function
curl https://your-site.netlify.app/health-simple

# Test with verbose output
curl -v https://your-site.netlify.app/health
```

## Debugging Steps

### 1. Check Build Logs
1. Go to Netlify dashboard
2. Deploys tab
3. Click on failed deploy
4. Review build logs for errors

### 2. Check Function Logs
1. Functions tab in dashboard
2. Click on function name
3. View real-time logs

### 3. Test Function Locally
```bash
# Clone your repo
git clone your-repo-url
cd your-repo

# Install dependencies
npm install

# Test locally
netlify dev
```

### 4. Validate Configuration
```bash
# Check netlify.toml syntax
netlify build --dry-run

# Validate function
netlify functions:list
```

## Alternative Deployment Methods

### 1. Manual Deploy
```bash
# Build locally
npm run build

# Deploy manually
netlify deploy --dir=dist --functions=netlify/functions --prod
```

### 2. GitHub Integration
1. Connect GitHub repository
2. Set build command: `npm ci && npm run build`
3. Set publish directory: `dist`
4. Set functions directory: `netlify/functions`

### 3. CLI Deploy
```bash
# One-time setup
netlify login
netlify link

# Deploy
npm run deploy:netlify
```

## Common Error Messages

### "Build failed: Failed to install dependencies"
- **Cause**: npm/Node.js version issues
- **Fix**: Update `.nvmrc` and `netlify.toml`

### "Function invocation failed"
- **Cause**: Missing dependencies or code errors
- **Fix**: Check function logs, use fallback endpoint

### "Build exceeded time limit"
- **Cause**: Slow dependency installation
- **Fix**: Use `npm ci`, clear cache, optimize dependencies

### "Function timeout"
- **Cause**: Health checks taking too long
- **Fix**: Reduce timeout, use simplified health check

## Best Practices

1. **Use specific Node.js version**:
   ```
   # .nvmrc
   18.20.8
   ```

2. **Optimize dependencies**:
   - Move runtime dependencies to `dependencies`
   - Keep build tools in `devDependencies`

3. **Handle errors gracefully**:
   ```javascript
   try {
     // Main functionality
   } catch (error) {
     // Fallback response
   }
   ```

4. **Use environment detection**:
   ```javascript
   if (process.env.NETLIFY) {
     // Netlify-specific optimizations
   }
   ```

5. **Monitor function performance**:
   - Check function execution time
   - Monitor error rates
   - Set up alerts

## Getting Help

If issues persist:

1. **Check Netlify Status**: https://www.netlifystatus.com/
2. **Netlify Community**: https://community.netlify.com/
3. **Support**: Contact Netlify support with:
   - Deploy ID
   - Error messages
   - Build logs
   - Function logs
