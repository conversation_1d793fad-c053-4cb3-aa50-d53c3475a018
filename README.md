# High-Speed BFF (Backend for Frontend) with Multi-Platform Support

A high-performance, production-ready Backend for Frontend service with Vite + React frontend and Node.js backend. Supports deployment on multiple serverless platforms including Vercel, Netlify, and Cloudflare Workers.

## Features

- **🚀 Vite + React Frontend**: Modern, fast, and reactive user interface
- **🌐 Multi-Platform Serverless**: Deploy on Vercel, Netlify, or Cloudflare Workers
- **⚖️ Intelligent Load Balancing**: Multiple strategies including weighted round-robin, least connections, and priority-based
- **🏥 Smart Health Checking**: Automatic backend health monitoring with platform-specific optimizations
- **🛡️ Security**: Rate limiting, CORS, helmet security headers
- **📝 Detailed Logging**: Structured logging with <PERSON> (platform-aware)
- **🔄 Auto-Failover**: Circuit breaker pattern for resilient backend communication
- **💾 Intelligent Caching**: Configurable response caching with TTL
- **📊 Real-time Monitoring**: Comprehensive metrics and alerting

## Quick Start

### Installation

```bash
# Install dependencies
npm install
```

### Development

```bash
# Run the Vite dev server for the frontend
npm run dev
```
The frontend will be available at `http://localhost:5173` (or another port if 5173 is busy). The Vite server will proxy API requests starting with `/api` to the backend server running on port 3001, which you should start separately.

### Running the API server

In a separate terminal, run:
```bash
# Start the API server
npm start
```

### Build

```bash
# Build the application for production
npm run build
```
This will create a `dist` directory with the optimized frontend assets.


## Project Structure

```
├── api/
│   ├── src/                # Original backend source
│   └── server.js           # Backend server entrypoint
├── src/
│   └── main.jsx            # React application entrypoint
├── .gitignore
├── index.html              # Vite entrypoint
├── vercel.json             # Vercel deployment configuration
├── vite.config.js          # Vite configuration
├── package.json
└── README.md
```

## Multi-Platform Deployment

This BFF supports deployment on multiple serverless platforms. Choose the one that best fits your needs:

### 🚀 Vercel (Recommended)

**Best for**: Easy deployment, excellent DX, automatic HTTPS

1. Push your code to a Git repository (GitHub, GitLab, Bitbucket)
2. Import the repository into your Vercel account
3. Vercel will automatically detect the `vercel.json` file and configure the build settings
4. Set environment variables in Vercel dashboard (optional):
   ```
   HEALTH_CHECK_TIMEOUT_OVERRIDE=15000
   LOG_LEVEL=info
   NODE_ENV=production
   ```
5. Deploy!

### 🌐 Netlify

**Best for**: JAMstack workflows, form handling, edge functions

1. Push your code to a Git repository
2. Connect your repository to Netlify
3. Netlify will use the `netlify.toml` configuration automatically
4. Install dependencies: `npm install serverless-http`
5. Set environment variables in Netlify dashboard:
   ```
   HEALTH_CHECK_TIMEOUT_OVERRIDE=15000
   LOG_LEVEL=info
   NODE_ENV=production
   ```
6. Deploy with: `npm run deploy:netlify`

**Troubleshooting**: If you encounter build issues, see [NETLIFY-TROUBLESHOOTING.md](./NETLIFY-TROUBLESHOOTING.md)

### ⚡ Cloudflare Workers

**Best for**: Global edge deployment, lowest latency, cost-effective

1. Install Wrangler CLI: `npm install -g wrangler`
2. Login to Cloudflare: `wrangler login`
3. Update `wrangler.toml` with your account details
4. Set environment variables in `wrangler.toml` or Cloudflare dashboard
5. Deploy with: `npm run deploy:workers`

**Note**: Cloudflare Workers uses a simplified adapter. For full functionality, consider using the Vercel or Netlify deployment.

## Testing and Validation

### Test Backend Connectivity
```bash
# Test if backends are reachable
npm run test:connectivity

# Test local health endpoint
npm run test:local

# Test specific deployment
npm run test:deployment https://your-deployment-url.com
```

### Multi-Platform Testing
```bash
# Test multiple platforms at once
npm run test:multi-platform vercel=https://your-project.vercel.app netlify=https://your-project.netlify.app

# Or set environment variables
export VERCEL_URL=https://your-project.vercel.app
export NETLIFY_URL=https://your-project.netlify.app
npm run test:multi-platform
```

### Available Test Scripts
- `npm run test:health` - Test backend health checks
- `npm run test:connectivity` - Test backend connectivity
- `npm run test:local` - Test local development server
- `npm run test:deployment <url>` - Test specific deployment
- `npm run test:multi-platform` - Test multiple platforms

## Backend Configuration

The backend configuration is still managed via `config.json`. Key settings include:

```json
{
  "server": {
    "port": 3001,
    "host": "0.0.0.0"
  },
  "backends": [
    {
      "id": "api-fast-ai",
      "url": "https://api.fast-ai.xyz",
      "priority": 1,
      "weight": 100,
      "healthCheck": {
        "enabled": true,
        "path": "/api/v1/guest/comm/config",
        "interval": 30000
      }
    }
  ]
}
```

## API Endpoints

The backend API remains the same.

### Health & Monitoring

- `GET /health` - Service health status
- `GET /metrics` - Comprehensive metrics

### API Proxy

All API requests are proxied to configured backends:

- `GET|POST|PUT|DELETE /api/v1/user/*` - User APIs (requires authentication)
- `GET|POST|PUT|DELETE /api/v1/guest/*` - Guest APIs (no auth required)
- `GET|POST|PUT|DELETE /api/v1/passport/*` - Authentication APIs

## License

MIT License - see LICENSE file for details.
