# Multi-Platform BFF Troubleshooting Guide

## "No healthy backends available" Error

This error occurs when the BFF cannot reach any of the configured backend services across any deployment platform. Here's how to diagnose and fix it.

### Quick Diagnosis

1. **Test backend connectivity locally:**
   ```bash
   npm run test:connectivity
   ```

2. **Test local BFF health endpoint:**
   ```bash
   npm start
   # In another terminal:
   npm run test:local
   ```

3. **Test deployed BFF (any platform):**
   ```bash
   # Test specific platform
   npm run test:deployment https://your-deployment-url.com

   # Test multiple platforms
   npm run test:multi-platform vercel=https://your-project.vercel.app netlify=https://your-project.netlify.app
   ```

### Common Causes and Solutions

#### 1. Network Connectivity Issues

**Symptoms:**
- Works locally but fails on serverless platforms
- Error codes: `ENOTFOUND`, `ECONNREFUSED`, `ETIMEDOUT`
- Different behavior across platforms

**Solutions:**
- Check if backend URLs are accessible from the internet
- Verify DNS resolution from different geographic locations
- Ensure backends accept requests from serverless platform IP ranges:
  - Vercel: Global edge network
  - Netlify: AWS-based infrastructure
  - Cloudflare: Global edge network

#### 2. Timeout Issues in Serverless Environment

**Symptoms:**
- `ETIMEDOUT` errors in platform logs
- Works with longer timeouts locally
- Different timeout behavior across platforms

**Solutions:**
- Set environment variables in your platform dashboard:
  ```
  HEALTH_CHECK_TIMEOUT_OVERRIDE=15000
  BACKEND_TIMEOUT_OVERRIDE=10000
  ```
- Platform-specific timeout limits:
  - Vercel: 10s (Hobby), 60s (Pro)
  - Netlify: 10s (free), 26s (paid)
  - Cloudflare Workers: 10ms CPU time (free), 50ms (paid)

#### 3. Backend Service Issues

**Symptoms:**
- HTTP 4xx/5xx responses
- Empty or malformed responses

**Solutions:**
- Check backend service status
- Verify health endpoint returns valid JSON
- Ensure health endpoint path is correct

#### 4. Serverless Cold Start Issues

**Symptoms:**
- First request fails, subsequent requests work
- Intermittent failures across platforms
- Slow initial response times

**Solutions:**
- The BFF performs on-demand health checks in serverless environments
- Health checks are forced when no recent checks exist
- Platform-specific cold start times:
  - Vercel: ~100ms
  - Netlify: ~200ms
  - Cloudflare Workers: ~5ms

### Environment Variables for All Platforms

Set these in your platform's dashboard under Environment Variables:

**Common Variables:**
```
NODE_ENV=production
LOG_LEVEL=info
HEALTH_CHECK_TIMEOUT_OVERRIDE=15000
BACKEND_TIMEOUT_OVERRIDE=10000
CIRCUIT_BREAKER_FAILURE_THRESHOLD=3
```

**Platform-Specific:**
- **Vercel**: Set in Project Settings > Environment Variables
- **Netlify**: Set in Site Settings > Environment Variables
- **Cloudflare Workers**: Set in `wrangler.toml` or Workers dashboard

### Debugging Steps

1. **Check Platform Function Logs:**
   - **Vercel**: Dashboard > Functions tab > View logs
   - **Netlify**: Dashboard > Functions tab > View logs
   - **Cloudflare Workers**: Dashboard > Workers > View logs

2. **Test Individual Backends:**
   ```bash
   curl -v https://api.fast-ai.xyz/api/v1/guest/comm/config
   curl -v https://get.fast-ai.xyz/api/v1/guest/comm/config
   ```

3. **Test with Extended Timeout:**
   ```bash
   curl -m 30 https://your-deployment-url.com/health
   ```

### Configuration Adjustments

If backends are consistently slow, update `config.json`:

```json
{
  "backends": [
    {
      "healthCheck": {
        "timeout": 15000,
        "interval": 60000,
        "retries": 3
      }
    }
  ],
  "loadBalancer": {
    "circuitBreaker": {
      "failureThreshold": 3,
      "resetTimeout": 30000
    }
  }
}
```

### Platform-Specific Optimizations

The BFF automatically detects the deployment environment and optimizes accordingly:

**All Serverless Platforms:**
- Disables cron-based health checks (uses on-demand instead)
- Increases timeouts automatically
- Forces health checks when needed

**Vercel:**
- Disables file logging
- Uses Express.js with serverless adapter

**Netlify:**
- Uses `serverless-http` adapter
- Optimized for Netlify Functions

**Cloudflare Workers:**
- Uses simplified adapter (limited Node.js APIs)
- Optimized for edge computing
- Minimal cold start time

### Getting Help

If the issue persists:

1. Run all test scripts and share results
2. Check platform-specific function logs
3. Verify backend accessibility from external networks
4. Test across multiple platforms to isolate platform-specific issues
5. For Netlify build issues, see `NETLIFY-TROUBLESHOOTING.md`
6. Consider using a monitoring service to check backend uptime

### Test Commands Summary

```bash
# Test backend connectivity
npm run test:connectivity

# Test local health endpoint
npm run test:local

# Test deployment (replace URL)
npm run test:deployment https://your-deployment-url.com

# Test multiple platforms
npm run test:multi-platform vercel=https://your-project.vercel.app netlify=https://your-project.netlify.app

# Test individual backend health
npm run test:health
```
