const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const config = require('../config.json');
const HealthChecker = require('./src/healthChecker');
const LoadBalancer = require('./src/loadBalancer');
const ApiProxy = require('./src/apiProxy');
const MonitoringService = require('./src/monitoring');
const logger = require('./src/logger');

class BFFServer {
  constructor() {
    this.app = express();
    this.config = config;
    this.healthChecker = null;
    this.loadBalancer = null;
    this.apiProxy = null;
    this.monitoring = null;
    
    this.setupMiddleware();
    this.initializeServices();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    
    // CORS
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
      credentials: true
    }));
    
    // Compression
    this.app.use(compression());
    
    // Rate limiting
    if (this.config.rateLimit) {
      const limiter = rateLimit({
        windowMs: this.config.rateLimit.windowMs,
        max: this.config.rateLimit.max,
        message: { error: this.config.rateLimit.message },
        standardHeaders: true,
        legacyHeaders: false
      });
      this.app.use(limiter);
    }
    
    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Request logging and monitoring
    this.app.use((req, res, next) => {
      req.startTime = Date.now();
      logger.debug(`Incoming request: ${req.method} ${req.url}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Capture response for monitoring
      const originalSend = res.send;
      res.send = function(data) {
        const responseTime = Date.now() - req.startTime;

        // Record request in monitoring (will be set up after services initialization)
        if (req.app.locals.monitoring) {
          req.app.locals.monitoring.recordRequest(req, res, responseTime, req.selectedBackend);
        }

        return originalSend.call(this, data);
      };

      next();
    });
  }

  initializeServices() {
    // Initialize monitoring service
    this.monitoring = new MonitoringService(this.config);

    // Initialize health checker
    this.healthChecker = new HealthChecker(this.config);

    // Initialize load balancer
    this.loadBalancer = new LoadBalancer(this.config, this.healthChecker);

    // Initialize API proxy
    this.apiProxy = new ApiProxy(this.loadBalancer, this.config);

    // Setup monitoring event listeners
    this.setupMonitoringListeners();

    // Make monitoring available to middleware
    this.app.locals.monitoring = this.monitoring;

    logger.info('BFF services initialized successfully');
  }

  setupMonitoringListeners() {
    // Listen for health check events
    this.healthChecker.on('healthChange', (event) => {
      this.monitoring.recordBackendHealth(
        event.backendId,
        event.isHealthy,
        event.backend.responseTime,
        event.error
      );
    });

    // Listen for monitoring alerts
    this.monitoring.on('error', (event) => {
      logger.error('Monitoring alert', event);
    });
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', async (req, res) => {
      try {
        logger.debug('Health check endpoint called');

        // Check if services are initialized
        if (!this.healthChecker || !this.loadBalancer || !this.apiProxy) {
          logger.error('Services not properly initialized');
          return res.status(503).json({
            status: 'unhealthy',
            error: 'Services not initialized',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            environment: this.detectEnvironment()
          });
        }

        // In serverless environments, force health check if no recent checks
        const isServerless = process.env.VERCEL || process.env.NETLIFY ||
                            process.env.CF_PAGES || process.env.CLOUDFLARE_WORKERS;
        if (isServerless && !this.healthChecker.hasRecentHealthChecks()) {
          logger.info(`Forcing health check in ${this.healthChecker.detectEnvironment()} environment`);
          await this.healthChecker.forceHealthCheckAll();
        }

        const healthStatus = this.healthChecker.getAllBackendStatus();
        const loadBalancerStats = this.loadBalancer.getStats();
        const cacheStats = this.apiProxy.getCacheStats();

        const overallHealth = Object.values(healthStatus).some(backend => backend.isHealthy);

        const response = {
          status: overallHealth ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          backends: healthStatus,
          loadBalancer: loadBalancerStats,
          cache: cacheStats,
          memory: process.memoryUsage(),
          version: require('../package.json').version,
          environment: this.detectEnvironment(),
          note: isServerless ? 'Health checks performed on-demand in serverless environment' : undefined
        };

        logger.debug('Health check response prepared', { status: response.status });
        res.status(overallHealth ? 200 : 503).json(response);

      } catch (error) {
        logger.error('Error in health check endpoint', error);
        res.status(500).json({
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          environment: this.detectEnvironment()
        });
      }
    });

    // Simple status endpoint for quick testing
    this.app.get('/status', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: this.detectEnvironment(),
        version: require('../package.json').version
      });
    });

    // Metrics endpoint
    this.app.get('/metrics', (req, res) => {
      const metrics = {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        backends: this.healthChecker.getAllBackendStatus(),
        loadBalancer: this.loadBalancer.getStats(),
        cache: this.apiProxy.getCacheStats(),
        performance: this.apiProxy.getPerformanceStats(),
        monitoring: this.monitoring.getMetrics(),
        system: {
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          platform: process.platform,
          nodeVersion: process.version
        }
      };

      res.json(metrics);
    });

    // Alerts endpoint
    this.app.get('/alerts', (req, res) => {
      const alerts = this.monitoring.getAlerts();
      res.json({
        alerts,
        count: alerts.length,
        timestamp: new Date().toISOString()
      });
    });

    // Recent errors endpoint
    this.app.get('/errors', (req, res) => {
      const limit = parseInt(req.query.limit) || 50;
      const errors = this.monitoring.getRecentErrors(limit);
      res.json({
        errors,
        count: errors.length,
        timestamp: new Date().toISOString()
      });
    });

    // Admin endpoints
    this.app.post('/admin/cache/clear', (req, res) => {
      this.apiProxy.clearCache();
      res.json({ message: 'Cache cleared successfully' });
    });

    this.app.post('/admin/stats/reset', (req, res) => {
      this.loadBalancer.resetStats();
      res.json({ message: 'Statistics reset successfully' });
    });

    // User API routes (require authentication)
    this.app.all('/api/v1/user/*', this.authenticateUser.bind(this), (req, res) => {
      this.apiProxy.proxyRequest(req, res);
    });

    // Guest API routes (no authentication required)
    this.app.all('/api/v1/guest/*', (req, res) => {
      this.apiProxy.proxyRequest(req, res);
    });

    // Passport API routes (no authentication required)
    this.app.all('/api/v1/passport/*', (req, res) => {
      this.apiProxy.proxyRequest(req, res);
    });

    // Catch-all for other API routes
    this.app.all('/api/*', (req, res) => {
      this.apiProxy.proxyRequest(req, res);
    });

    
  }

  authenticateUser(req, res, next) {
    const authHeader = req.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please provide a valid Bearer token'
      });
    }

    // Extract token (remove 'Bearer ' prefix)
    const token = authHeader.substring(7);
    
    if (!token) {
      return res.status(401).json({
        error: 'Invalid token',
        message: 'Token cannot be empty'
      });
    }

    // Add token to request for forwarding
    req.authToken = token;
    next();
  }

  setupErrorHandling() {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.url} not found`,
        timestamp: new Date().toISOString()
      });
    });

    // Global error handler
    this.app.use((err, req, res, next) => {
      logger.logError(err, {
        url: req.url,
        method: req.method,
        ip: req.ip
      });

      res.status(err.status || 500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'production' ? 'Something went wrong' : err.message,
        timestamp: new Date().toISOString()
      });
    });
  }

  start() {
    const port = this.config.server?.port || 3000;
    const host = this.config.server?.host || '0.0.0.0';

    this.server = this.app.listen(port, host, () => {
      logger.info(`BFF Server started on ${host}:${port}`);
      logger.info('Health checks initialized and running');
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      this.shutdown();
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      this.shutdown();
    });

    return this.server;
  }

  // Detect deployment environment
  detectEnvironment() {
    if (process.env.VERCEL) return 'vercel';
    if (process.env.NETLIFY) return 'netlify';
    if (process.env.CF_PAGES) return 'cloudflare-pages';
    if (process.env.CLOUDFLARE_WORKERS) return 'cloudflare-workers';
    return 'local';
  }

  shutdown() {
    if (this.healthChecker) {
      this.healthChecker.stop();
    }

    if (this.server) {
      this.server.close(() => {
        logger.info('Server closed');
        process.exit(0);
      });
    }
  }
}

// Platform detection and export
function detectPlatform() {
  if (process.env.VERCEL) return 'vercel';
  if (process.env.NETLIFY) return 'netlify';
  if (process.env.CF_PAGES || process.env.CLOUDFLARE_WORKERS) return 'cloudflare';
  return 'local';
}

const platform = detectPlatform();

// Export based on platform
if (platform === 'vercel' || platform === 'netlify') {
  // Serverless environments - export app directly
  module.exports = new BFFServer().app;
} else if (platform === 'cloudflare') {
  // Cloudflare Workers - export handler
  const app = new BFFServer().app;
  module.exports = {
    app,
    fetch: async (request, env, ctx) => {
      // Convert Cloudflare Workers request to Express-compatible format
      return handleCloudflareRequest(app, request, env, ctx);
    }
  };
} else {
  // Local development and traditional deployments - export class
  module.exports = BFFServer;
}

// Cloudflare Workers request handler
async function handleCloudflareRequest(app, request, env, ctx) {
  // This is a simplified handler - you might want to use a proper adapter
  const url = new URL(request.url);
  const method = request.method;
  const headers = Object.fromEntries(request.headers.entries());

  return new Promise((resolve) => {
    const mockReq = {
      method,
      url: url.pathname + url.search,
      headers,
      body: request.body
    };

    const mockRes = {
      statusCode: 200,
      headers: {},
      body: '',
      status: function(code) { this.statusCode = code; return this; },
      json: function(data) {
        this.body = JSON.stringify(data);
        this.headers['content-type'] = 'application/json';
        return this;
      },
      send: function(data) {
        this.body = data;
        return this;
      }
    };

    // Process request through Express app
    app(mockReq, mockRes);

    // Convert to Cloudflare Response
    resolve(new Response(mockRes.body, {
      status: mockRes.statusCode,
      headers: mockRes.headers
    }));
  });
}
