const axios = require('axios');
const logger = require('./logger');


class ApiProxy {
  constructor(loadBalancer, config) {
    this.loadBalancer = loadBalancer;
    this.config = config;
    this.cache = new Map();
    this.cacheEnabled = config.cache?.enabled || false;
    this.cacheTTL = config.cache?.ttl || 300000; // 5 minutes default
  }

  async proxyRequest(req, res, options = {}) {
    const startTime = Date.now();
    let selectedBackend = null;
    let attempts = 0;
    const maxAttempts = 3;
    const excludeBackends = [];

    while (attempts < maxAttempts) {
      attempts++;

      // Select a backend (excluding failed ones)
      selectedBackend = await this.loadBalancer.selectBackendWithFailover(excludeBackends);

      if (!selectedBackend) {
        return this.sendError(res, 503, 'No healthy backends available', startTime);
      }

      try {
        // Check cache first for GET requests
        if (req.method === 'GET' && this.cacheEnabled) {
          const cacheKey = this.generateCacheKey(req, selectedBackend);
          const cachedResponse = this.getFromCache(cacheKey);
          
          if (cachedResponse) {
            logger.debug(`Cache hit for ${req.url}`);
            const responseTime = Date.now() - startTime;
            logger.logRequest(req, { statusCode: 200 }, responseTime, selectedBackend);
            return res.json(cachedResponse);
          }
        }

        // Prepare request configuration
        const requestConfig = {
          method: req.method,
          url: `${selectedBackend.url}${req.url}`,
          headers: this.prepareHeaders(req),
          timeout: selectedBackend.timeout || 5000,
          validateStatus: (status) => status < 500, // Don't throw on 4xx errors
          ...options
        };

        // Add request body for non-GET requests
        if (req.method !== 'GET' && req.body) {
          requestConfig.data = req.body;
        }

        // Add query parameters
        if (req.query && Object.keys(req.query).length > 0) {
          requestConfig.params = req.query;
        }

        // Make the request using axios
        const response = await axios(requestConfig);

        const responseTime = Date.now() - startTime;

        // Release connection count
        this.loadBalancer.releaseConnection(selectedBackend.id);

        // Cache successful GET responses
        if (req.method === 'GET' && this.cacheEnabled && response.status === 200) {
          const cacheKey = this.generateCacheKey(req, selectedBackend);
          this.setCache(cacheKey, response.data);
        }

        // Record backend performance for load balancer optimization
        this.loadBalancer.recordBackendPerformance(selectedBackend.id, responseTime, true);

        // Log successful request
        logger.logRequest(req, response, responseTime, selectedBackend);

        // Send response
        res.status(response.status);

        // Copy relevant headers
        this.copyResponseHeaders(response, res);

        return res.json(response.data);

      } catch (error) {
        const responseTime = Date.now() - startTime;
        
        // Record backend performance failure for load balancer optimization
        this.loadBalancer.recordBackendPerformance(selectedBackend.id, responseTime, false);

        // Release connection count
        this.loadBalancer.releaseConnection(selectedBackend.id);

        // Log error
        logger.error(`Request failed to backend ${selectedBackend.id}`, {
          backend: selectedBackend.id,
          url: selectedBackend.url,
          error: error.message,
          attempt: attempts,
          responseTime
        });

        // Add this backend to exclude list for retry
        excludeBackends.push(selectedBackend.id);

        // If this was the last attempt or no more backends available, return error
        if (attempts >= maxAttempts || excludeBackends.length >= this.loadBalancer.healthChecker.getHealthyBackends().length) {
          const statusCode = error.response?.status || 500;
          const errorMessage = error.response?.data?.message || error.message || 'Backend request failed';
          
          logger.logRequest(req, { statusCode }, responseTime, selectedBackend);
          return this.sendError(res, statusCode, errorMessage, startTime, error.response?.data);
        }

        // Continue to next attempt with different backend
        logger.info(`Retrying request with different backend (attempt ${attempts + 1}/${maxAttempts})`);
      }
    }
  }

  prepareHeaders(req) {
    const headers = { ...req.headers };
    
    // Remove hop-by-hop headers
    delete headers.host;
    delete headers.connection;
    delete headers['transfer-encoding'];
    delete headers['content-length'];
    
    // Add forwarded headers
    headers['x-forwarded-for'] = req.ip;
    headers['x-forwarded-proto'] = req.protocol;
    headers['x-forwarded-host'] = req.get('host');
    
    return headers;
  }

  copyResponseHeaders(response, res) {
    const headersToSkip = [
      'connection',
      'transfer-encoding',
      'content-encoding',
      'content-length'
    ];

    Object.keys(response.headers).forEach(header => {
      if (!headersToSkip.includes(header.toLowerCase())) {
        res.set(header, response.headers[header]);
      }
    });
  }

  generateCacheKey(req, backend) {
    const url = req.url;
    const query = JSON.stringify(req.query || {});
    const auth = req.get('authorization') || '';
    return `${backend.id}:${url}:${query}:${auth}`;
  }

  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  setCache(key, data) {
    // Implement simple LRU by removing oldest entries when cache is full
    if (this.cache.size >= (this.config.cache?.maxSize || 1000)) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearCache() {
    this.cache.clear();
    logger.info('API proxy cache cleared');
  }

  sendError(res, statusCode, message, startTime, data = null) {
    const responseTime = Date.now() - startTime;
    
    const errorResponse = {
      error: true,
      message,
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime}ms`
    };

    if (data) {
      errorResponse.details = data;
    }

    logger.error(`API error response: ${statusCode} - ${message}`, {
      statusCode,
      responseTime
    });

    return res.status(statusCode).json(errorResponse);
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.config.cache?.maxSize || 1000,
      enabled: this.cacheEnabled,
      ttl: this.cacheTTL
    };
  }

  
}

module.exports = ApiProxy;
