const axios = require('axios');
const cron = require('node-cron');
const EventEmitter = require('events');
const logger = require('./logger');

class HealthChecker extends EventEmitter {
  constructor(config) {
    super();
    this.config = config;
    this.backends = new Map();
    this.healthCheckJobs = new Map();
    this.circuitBreakers = new Map();
    this.proactiveHealthChecks = new Map(); // For faster health detection
    this.backupHealthEndpoints = new Map(); // Alternative health check endpoints
    this.adaptiveIntervals = new Map(); // Dynamic health check intervals

    // Initialize backends
    this.initializeBackends();
    this.initializeBackupEndpoints();
    this.startHealthChecks();
    this.startProactiveHealthChecks();
  }

  initializeBackends() {
    this.config.backends.forEach(backend => {
      this.backends.set(backend.id, {
        ...backend,
        status: 'unknown',
        lastCheck: null,
        consecutiveFailures: 0,
        responseTime: null,
        isHealthy: false,
        healthHistory: [], // Track health check history
        lastSuccessfulCheck: null,
        failureStreak: 0
      });

      // Initialize circuit breaker with more aggressive settings
      this.circuitBreakers.set(backend.id, {
        state: 'closed', // closed, open, half-open
        failures: 0,
        lastFailureTime: null,
        nextAttempt: null,
        halfOpenAttempts: 0,
        maxHalfOpenAttempts: 3
      });

      // Initialize adaptive intervals (start with base interval)
      this.adaptiveIntervals.set(backend.id, backend.healthCheck.interval);
    });
  }

  initializeBackupEndpoints() {
    this.config.backends.forEach(backend => {
      // Define backup health check endpoints
      const backupEndpoints = [
        '/api/v1/guest/comm/config', // Primary
        '/health',                   // Standard health endpoint
        '/api/health',              // Alternative health endpoint
        '/status',                  // Status endpoint
        '/'                         // Root endpoint as last resort
      ];

      this.backupHealthEndpoints.set(backend.id, backupEndpoints);
    });
  }

  startHealthChecks() {
    this.config.backends.forEach(backend => {
      if (backend.healthCheck.enabled) {
        // In serverless environments, skip cron jobs and rely on on-demand checks
        const isServerless = process.env.VERCEL || process.env.NETLIFY ||
                            process.env.CF_PAGES || process.env.CLOUDFLARE_WORKERS;

        if (!isServerless) {
          // Use adaptive interval for health checks
          const adaptiveInterval = this.adaptiveIntervals.get(backend.id);
          const cronExpression = this.intervalToCron(adaptiveInterval);

          const job = cron.schedule(cronExpression, async () => {
            await this.checkBackendHealth(backend.id);
            this.adjustHealthCheckInterval(backend.id);
          }, {
            scheduled: false
          });

          this.healthCheckJobs.set(backend.id, job);
          job.start();
        }

        // Perform initial health check (important for serverless environments)
        this.checkBackendHealth(backend.id);
      }
    });
  }

  startProactiveHealthChecks() {
    // Start proactive health monitoring for faster failure detection
    this.config.backends.forEach(backend => {
      if (backend.healthCheck.enabled) {
        const proactiveInterval = Math.min(10000, backend.healthCheck.interval / 3); // Check every 10s or 1/3 of normal interval

        const proactiveJob = setInterval(async () => {
          const backendData = this.backends.get(backend.id);

          // Only do proactive checks if backend was recently healthy
          if (backendData && backendData.isHealthy) {
            await this.lightweightHealthCheck(backend.id);
          }
        }, proactiveInterval);

        this.proactiveHealthChecks.set(backend.id, proactiveJob);
      }
    });
  }

  intervalToCron(intervalMs) {
    const seconds = Math.floor(intervalMs / 1000);
    if (seconds < 60) {
      return `*/${seconds} * * * * *`;
    }
    const minutes = Math.floor(seconds / 60);
    return `*/${minutes} * * * *`;
  }

  async checkBackendHealth(backendId) {
    const backend = this.backends.get(backendId);
    if (!backend) return;

    const circuitBreaker = this.circuitBreakers.get(backendId);
    
    // Skip if circuit breaker is open and not ready for retry
    if (circuitBreaker.state === 'open' && Date.now() < circuitBreaker.nextAttempt) {
      return;
    }

    const startTime = Date.now();
    let isHealthy = false;
    let error = null;

    try {
      const healthUrl = `${backend.url}${backend.healthCheck.path}`;

      // Increase timeout for serverless environments or use environment override
      const baseTimeout = backend.healthCheck.timeout;
      const envTimeout = process.env.HEALTH_CHECK_TIMEOUT_OVERRIDE ?
        parseInt(process.env.HEALTH_CHECK_TIMEOUT_OVERRIDE) : null;

      // Detect serverless environment and adjust timeout
      const isServerless = process.env.VERCEL || process.env.NETLIFY ||
                          process.env.CF_PAGES || process.env.CLOUDFLARE_WORKERS;
      const serverlessTimeout = isServerless ?
        Math.max(baseTimeout, 15000) : baseTimeout; // At least 15s for serverless

      const timeout = envTimeout || serverlessTimeout;

      logger.debug(`Checking backend health: ${healthUrl} (timeout: ${timeout}ms)`);

      const response = await axios.get(healthUrl, {
        timeout,
        validateStatus: (status) => status >= 200 && status < 300,
        headers: {
          'User-Agent': 'BFF-HealthChecker/1.0',
          'Accept': 'application/json'
        }
      });

      // Validate that the response contains expected config data
      const responseData = response.data;
      const hasValidConfig = responseData && (
        typeof responseData === 'object' &&
        (responseData.data || responseData.config || Object.keys(responseData).length > 0)
      );

      if (hasValidConfig) {
        isHealthy = true;
        backend.responseTime = Date.now() - startTime;
        backend.consecutiveFailures = 0;

        logger.debug(`Backend ${backend.id} health check successful`, {
          responseTime: backend.responseTime,
          configKeys: Object.keys(responseData).slice(0, 5) // Log first 5 keys for debugging
        });
      } else {
        throw new Error('Invalid config response - empty or malformed data');
      }
      
      // Enhanced circuit breaker recovery logic
      if (circuitBreaker.state === 'half-open') {
        circuitBreaker.halfOpenAttempts++;

        // Require multiple successful attempts before fully closing
        if (circuitBreaker.halfOpenAttempts >= circuitBreaker.maxHalfOpenAttempts) {
          circuitBreaker.state = 'closed';
          circuitBreaker.failures = 0;
          circuitBreaker.halfOpenAttempts = 0;
          logger.info(`Circuit breaker closed for backend ${backendId} after ${circuitBreaker.halfOpenAttempts} successful attempts`);
        } else {
          logger.debug(`Circuit breaker half-open success ${circuitBreaker.halfOpenAttempts}/${circuitBreaker.maxHalfOpenAttempts} for backend ${backendId}`);
        }
      } else if (circuitBreaker.state === 'open') {
        // Reset failure count on first success after being open
        circuitBreaker.failures = 0;
      }

    } catch (err) {
      error = err;
      isHealthy = false;
      backend.consecutiveFailures++;
      backend.responseTime = null;

      // Enhanced error logging for debugging
      logger.error(`Backend ${backendId} health check failed`, {
        backendId,
        url: backend.url,
        healthUrl: `${backend.url}${backend.healthCheck.path}`,
        error: err.message,
        code: err.code,
        status: err.response?.status,
        timeout: isServerless ? 'extended' : 'normal',
        environment: this.detectEnvironment()
      });

      // Enhanced circuit breaker logic
      circuitBreaker.failures++;
      circuitBreaker.lastFailureTime = Date.now();

      // Progressive circuit breaker thresholds
      const failureThreshold = this.getAdaptiveFailureThreshold(backendId);

      if (circuitBreaker.failures >= failureThreshold) {
        if (circuitBreaker.state === 'closed') {
          circuitBreaker.state = 'open';
          // Adaptive reset timeout based on failure history
          const resetTimeout = this.calculateAdaptiveResetTimeout(backendId);
          circuitBreaker.nextAttempt = Date.now() + resetTimeout;
          logger.warn(`Circuit breaker opened for backend ${backendId} (failures: ${circuitBreaker.failures}, timeout: ${resetTimeout}ms)`);
        } else if (circuitBreaker.state === 'half-open') {
          // Failed in half-open state, go back to open with longer timeout
          circuitBreaker.state = 'open';
          const extendedTimeout = this.calculateAdaptiveResetTimeout(backendId) * 2;
          circuitBreaker.nextAttempt = Date.now() + extendedTimeout;
          circuitBreaker.halfOpenAttempts = 0;
          logger.warn(`Circuit breaker reopened for backend ${backendId} after half-open failure`);
        }
      }
    }

    // Update backend status
    const wasHealthy = backend.isHealthy;
    backend.isHealthy = isHealthy;
    backend.status = isHealthy ? 'healthy' : 'unhealthy';
    backend.lastCheck = new Date();

    // Log status change
    if (wasHealthy !== isHealthy) {
      const status = isHealthy ? 'recovered' : 'failed';
      logger.info(`Backend ${backendId} health check ${status}`, {
        backendId,
        url: backend.url,
        responseTime: backend.responseTime,
        consecutiveFailures: backend.consecutiveFailures,
        error: error?.message
      });

      this.emit('healthChange', {
        backendId,
        isHealthy,
        backend: { ...backend }
      });
    }

    // Attempt to transition circuit breaker from open to half-open
    if (circuitBreaker.state === 'open' && Date.now() >= circuitBreaker.nextAttempt) {
      circuitBreaker.state = 'half-open';
      logger.info(`Circuit breaker half-open for backend ${backendId}`);
    }
  }

  getHealthyBackends() {
    return Array.from(this.backends.values()).filter(backend => backend.isHealthy);
  }

  // Lightweight health check for proactive monitoring
  async lightweightHealthCheck(backendId) {
    const backend = this.backends.get(backendId);
    if (!backend) return;

    try {
      // Quick HEAD request to check if backend is responsive
      const response = await axios.head(`${backend.url}/api/v1/guest/comm/config`, {
        timeout: 3000,
        validateStatus: (status) => status >= 200 && status < 400
      });

      if (response.status >= 200 && response.status < 400) {
        // Backend is responsive, no need to update full health status
        logger.debug(`Proactive health check passed for backend ${backendId}`);
      }
    } catch (error) {
      // If lightweight check fails, trigger full health check
      logger.warn(`Proactive health check failed for backend ${backendId}, triggering full check`);
      await this.checkBackendHealth(backendId);
    }
  }

  // Adjust health check intervals based on backend performance
  adjustHealthCheckInterval(backendId) {
    const backend = this.backends.get(backendId);
    if (!backend) return;

    const baseInterval = backend.healthCheck.interval;
    let newInterval = baseInterval;

    if (backend.isHealthy) {
      // If healthy, can check less frequently
      if (backend.consecutiveFailures === 0 && backend.responseTime < 1000) {
        newInterval = Math.min(baseInterval * 2, 60000); // Max 60 seconds
      }
    } else {
      // If unhealthy, check more frequently
      newInterval = Math.max(baseInterval / 2, 5000); // Min 5 seconds
    }

    const currentInterval = this.adaptiveIntervals.get(backendId);
    if (Math.abs(currentInterval - newInterval) > 1000) {
      this.adaptiveIntervals.set(backendId, newInterval);
      logger.debug(`Adjusted health check interval for backend ${backendId}: ${currentInterval}ms -> ${newInterval}ms`);

      // Restart health check job with new interval if not in serverless environment
      const isServerless = process.env.VERCEL || process.env.NETLIFY ||
                          process.env.CF_PAGES || process.env.CLOUDFLARE_WORKERS;

      if (!isServerless) {
        this.restartHealthCheckJob(backendId, newInterval);
      }
    }
  }

  // Restart health check job with new interval
  restartHealthCheckJob(backendId, newInterval) {
    const existingJob = this.healthCheckJobs.get(backendId);
    if (existingJob) {
      existingJob.stop();
    }

    const cronExpression = this.intervalToCron(newInterval);
    const job = cron.schedule(cronExpression, async () => {
      await this.checkBackendHealth(backendId);
      this.adjustHealthCheckInterval(backendId);
    }, {
      scheduled: false
    });

    this.healthCheckJobs.set(backendId, job);
    job.start();
  }

  getBackendStatus(backendId) {
    return this.backends.get(backendId);
  }

  getAllBackendStatus() {
    const status = {};
    this.backends.forEach((backend, id) => {
      status[id] = {
        id: backend.id,
        url: backend.url,
        status: backend.status,
        isHealthy: backend.isHealthy,
        lastCheck: backend.lastCheck,
        responseTime: backend.responseTime,
        consecutiveFailures: backend.consecutiveFailures,
        circuitBreaker: this.circuitBreakers.get(id)
      };
    });
    return status;
  }

  // Force health check for all backends (useful for Vercel on-demand checks)
  async forceHealthCheckAll() {
    const promises = [];
    this.config.backends.forEach(backend => {
      if (backend.healthCheck.enabled) {
        promises.push(this.checkBackendHealth(backend.id));
      }
    });

    await Promise.allSettled(promises);
    return this.getAllBackendStatus();
  }

  // Check if any backend was checked recently (within last 5 minutes)
  hasRecentHealthChecks() {
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    return Array.from(this.backends.values()).some(backend =>
      backend.lastCheck && new Date(backend.lastCheck).getTime() > fiveMinutesAgo
    );
  }

  // Detect deployment environment
  detectEnvironment() {
    if (process.env.VERCEL) return 'vercel';
    if (process.env.NETLIFY) return 'netlify';
    if (process.env.CF_PAGES) return 'cloudflare-pages';
    if (process.env.CLOUDFLARE_WORKERS) return 'cloudflare-workers';
    return 'local';
  }

  // Calculate adaptive failure threshold based on backend history
  getAdaptiveFailureThreshold(backendId) {
    const backend = this.backends.get(backendId);
    const baseThreshold = this.config.loadBalancer.circuitBreaker.failureThreshold;

    if (!backend || !backend.healthHistory) {
      return baseThreshold;
    }

    // If backend has been consistently stable, allow more failures before opening
    const recentHistory = backend.healthHistory.slice(-20); // Last 20 checks
    const successRate = recentHistory.filter(h => h.success).length / recentHistory.length;

    if (successRate > 0.9) {
      return Math.min(baseThreshold * 2, 10); // Allow up to 10 failures for very stable backends
    } else if (successRate < 0.5) {
      return Math.max(Math.floor(baseThreshold / 2), 2); // Be more aggressive for unstable backends
    }

    return baseThreshold;
  }

  // Calculate adaptive reset timeout based on failure patterns
  calculateAdaptiveResetTimeout(backendId) {
    const backend = this.backends.get(backendId);
    const circuitBreaker = this.circuitBreakers.get(backendId);
    const baseTimeout = this.config.loadBalancer.circuitBreaker.resetTimeout;

    if (!backend || !circuitBreaker) {
      return baseTimeout;
    }

    // Exponential backoff based on consecutive circuit breaker openings
    const failureStreak = backend.failureStreak || 0;
    const backoffMultiplier = Math.min(Math.pow(1.5, failureStreak), 8); // Max 8x backoff

    // Consider response time - slower backends get longer timeouts
    const responseTimeFactor = backend.responseTime ? Math.min(backend.responseTime / 1000, 3) : 1;

    const adaptiveTimeout = baseTimeout * backoffMultiplier * responseTimeFactor;
    return Math.min(adaptiveTimeout, 300000); // Max 5 minutes
  }

  stop() {
    // Stop all health check jobs
    this.healthCheckJobs.forEach(job => job.stop());
    this.healthCheckJobs.clear();

    // Stop proactive health checks
    this.proactiveHealthChecks.forEach(job => clearInterval(job));
    this.proactiveHealthChecks.clear();

    logger.info('Health checker stopped');
  }
}

module.exports = HealthChecker;
