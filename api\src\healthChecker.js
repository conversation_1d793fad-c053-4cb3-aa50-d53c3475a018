const axios = require('axios');
const cron = require('node-cron');
const EventEmitter = require('events');
const logger = require('./logger');

class HealthChecker extends EventEmitter {
  constructor(config) {
    super();
    this.config = config;
    this.backends = new Map();
    this.healthCheckJobs = new Map();
    this.circuitBreakers = new Map();
    
    // Initialize backends
    this.initializeBackends();
    this.startHealthChecks();
  }

  initializeBackends() {
    this.config.backends.forEach(backend => {
      this.backends.set(backend.id, {
        ...backend,
        status: 'unknown',
        lastCheck: null,
        consecutiveFailures: 0,
        responseTime: null,
        isHealthy: false
      });

      // Initialize circuit breaker
      this.circuitBreakers.set(backend.id, {
        state: 'closed', // closed, open, half-open
        failures: 0,
        lastFailureTime: null,
        nextAttempt: null
      });
    });
  }

  startHealthChecks() {
    this.config.backends.forEach(backend => {
      if (backend.healthCheck.enabled) {
        // In serverless environments, skip cron jobs and rely on on-demand checks
        const isServerless = process.env.VERCEL || process.env.NETLIFY ||
                            process.env.CF_PAGES || process.env.CLOUDFLARE_WORKERS;

        if (!isServerless) {
          const cronExpression = this.intervalToCron(backend.healthCheck.interval);

          const job = cron.schedule(cronExpression, async () => {
            await this.checkBackendHealth(backend.id);
          }, {
            scheduled: false
          });

          this.healthCheckJobs.set(backend.id, job);
          job.start();
        }

        // Perform initial health check (important for serverless environments)
        this.checkBackendHealth(backend.id);
      }
    });
  }

  intervalToCron(intervalMs) {
    const seconds = Math.floor(intervalMs / 1000);
    if (seconds < 60) {
      return `*/${seconds} * * * * *`;
    }
    const minutes = Math.floor(seconds / 60);
    return `*/${minutes} * * * *`;
  }

  async checkBackendHealth(backendId) {
    const backend = this.backends.get(backendId);
    if (!backend) return;

    const circuitBreaker = this.circuitBreakers.get(backendId);
    
    // Skip if circuit breaker is open and not ready for retry
    if (circuitBreaker.state === 'open' && Date.now() < circuitBreaker.nextAttempt) {
      return;
    }

    const startTime = Date.now();
    let isHealthy = false;
    let error = null;

    try {
      const healthUrl = `${backend.url}${backend.healthCheck.path}`;

      // Increase timeout for serverless environments or use environment override
      const baseTimeout = backend.healthCheck.timeout;
      const envTimeout = process.env.HEALTH_CHECK_TIMEOUT_OVERRIDE ?
        parseInt(process.env.HEALTH_CHECK_TIMEOUT_OVERRIDE) : null;

      // Detect serverless environment and adjust timeout
      const isServerless = process.env.VERCEL || process.env.NETLIFY ||
                          process.env.CF_PAGES || process.env.CLOUDFLARE_WORKERS;
      const serverlessTimeout = isServerless ?
        Math.max(baseTimeout, 15000) : baseTimeout; // At least 15s for serverless

      const timeout = envTimeout || serverlessTimeout;

      logger.debug(`Checking backend health: ${healthUrl} (timeout: ${timeout}ms)`);

      const response = await axios.get(healthUrl, {
        timeout,
        validateStatus: (status) => status >= 200 && status < 300,
        headers: {
          'User-Agent': 'BFF-HealthChecker/1.0',
          'Accept': 'application/json'
        }
      });

      // Validate that the response contains expected config data
      const responseData = response.data;
      const hasValidConfig = responseData && (
        typeof responseData === 'object' &&
        (responseData.data || responseData.config || Object.keys(responseData).length > 0)
      );

      if (hasValidConfig) {
        isHealthy = true;
        backend.responseTime = Date.now() - startTime;
        backend.consecutiveFailures = 0;

        logger.debug(`Backend ${backend.id} health check successful`, {
          responseTime: backend.responseTime,
          configKeys: Object.keys(responseData).slice(0, 5) // Log first 5 keys for debugging
        });
      } else {
        throw new Error('Invalid config response - empty or malformed data');
      }
      
      // Reset circuit breaker on success
      if (circuitBreaker.state === 'half-open') {
        circuitBreaker.state = 'closed';
        circuitBreaker.failures = 0;
        logger.info(`Circuit breaker closed for backend ${backendId}`);
      }

    } catch (err) {
      error = err;
      isHealthy = false;
      backend.consecutiveFailures++;
      backend.responseTime = null;

      // Enhanced error logging for debugging
      logger.error(`Backend ${backendId} health check failed`, {
        backendId,
        url: backend.url,
        healthUrl: `${backend.url}${backend.healthCheck.path}`,
        error: err.message,
        code: err.code,
        status: err.response?.status,
        timeout: isServerless ? 'extended' : 'normal',
        environment: this.detectEnvironment()
      });

      // Update circuit breaker
      circuitBreaker.failures++;
      circuitBreaker.lastFailureTime = Date.now();

      if (circuitBreaker.failures >= this.config.loadBalancer.circuitBreaker.failureThreshold) {
        if (circuitBreaker.state === 'closed') {
          circuitBreaker.state = 'open';
          circuitBreaker.nextAttempt = Date.now() + this.config.loadBalancer.circuitBreaker.resetTimeout;
          logger.warn(`Circuit breaker opened for backend ${backendId}`);
        }
      }
    }

    // Update backend status
    const wasHealthy = backend.isHealthy;
    backend.isHealthy = isHealthy;
    backend.status = isHealthy ? 'healthy' : 'unhealthy';
    backend.lastCheck = new Date();

    // Log status change
    if (wasHealthy !== isHealthy) {
      const status = isHealthy ? 'recovered' : 'failed';
      logger.info(`Backend ${backendId} health check ${status}`, {
        backendId,
        url: backend.url,
        responseTime: backend.responseTime,
        consecutiveFailures: backend.consecutiveFailures,
        error: error?.message
      });

      this.emit('healthChange', {
        backendId,
        isHealthy,
        backend: { ...backend }
      });
    }

    // Attempt to transition circuit breaker from open to half-open
    if (circuitBreaker.state === 'open' && Date.now() >= circuitBreaker.nextAttempt) {
      circuitBreaker.state = 'half-open';
      logger.info(`Circuit breaker half-open for backend ${backendId}`);
    }
  }

  getHealthyBackends() {
    return Array.from(this.backends.values()).filter(backend => backend.isHealthy);
  }

  getBackendStatus(backendId) {
    return this.backends.get(backendId);
  }

  getAllBackendStatus() {
    const status = {};
    this.backends.forEach((backend, id) => {
      status[id] = {
        id: backend.id,
        url: backend.url,
        status: backend.status,
        isHealthy: backend.isHealthy,
        lastCheck: backend.lastCheck,
        responseTime: backend.responseTime,
        consecutiveFailures: backend.consecutiveFailures,
        circuitBreaker: this.circuitBreakers.get(id)
      };
    });
    return status;
  }

  // Force health check for all backends (useful for Vercel on-demand checks)
  async forceHealthCheckAll() {
    const promises = [];
    this.config.backends.forEach(backend => {
      if (backend.healthCheck.enabled) {
        promises.push(this.checkBackendHealth(backend.id));
      }
    });

    await Promise.allSettled(promises);
    return this.getAllBackendStatus();
  }

  // Check if any backend was checked recently (within last 5 minutes)
  hasRecentHealthChecks() {
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    return Array.from(this.backends.values()).some(backend =>
      backend.lastCheck && new Date(backend.lastCheck).getTime() > fiveMinutesAgo
    );
  }

  // Detect deployment environment
  detectEnvironment() {
    if (process.env.VERCEL) return 'vercel';
    if (process.env.NETLIFY) return 'netlify';
    if (process.env.CF_PAGES) return 'cloudflare-pages';
    if (process.env.CLOUDFLARE_WORKERS) return 'cloudflare-workers';
    return 'local';
  }

  stop() {
    this.healthCheckJobs.forEach(job => job.stop());
    this.healthCheckJobs.clear();
  }
}

module.exports = HealthChecker;
