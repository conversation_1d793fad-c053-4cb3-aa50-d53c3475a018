const logger = require('./logger');

class LoadBalancer {
  constructor(config, healthChecker) {
    this.config = config;
    this.healthChecker = healthChecker;
    this.currentIndex = 0;
    this.weightedList = [];
    this.requestCounts = new Map();
    this.responseTimeHistory = new Map(); // Track response times for adaptive weights
    this.backendPerformance = new Map(); // Track backend performance metrics
    this.adaptiveWeights = new Map(); // Dynamic weights based on performance
    this.lastWeightUpdate = Date.now();
    this.weightUpdateInterval = 30000; // Update weights every 30 seconds

    this.initializeBackendMetrics();
    this.initializeWeightedList();

    // Listen for health changes to update weighted list
    this.healthChecker.on('healthChange', () => {
      this.initializeWeightedList();
    });

    // Start adaptive weight adjustment
    this.startAdaptiveWeightAdjustment();
  }

  initializeBackendMetrics() {
    this.config.backends.forEach(backend => {
      this.responseTimeHistory.set(backend.id, []);
      this.backendPerformance.set(backend.id, {
        avgResponseTime: 0,
        successRate: 1.0,
        recentRequests: [],
        adaptiveWeight: backend.weight,
        lastUpdate: Date.now()
      });
      this.adaptiveWeights.set(backend.id, backend.weight);
    });
  }

  initializeWeightedList() {
    this.weightedList = [];
    const healthyBackends = this.healthChecker.getHealthyBackends();

    // Sort by priority first, then by adaptive performance
    healthyBackends.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      // Secondary sort by performance (lower response time = higher priority)
      const perfA = this.backendPerformance.get(a.id);
      const perfB = this.backendPerformance.get(b.id);
      return (perfA?.avgResponseTime || 1000) - (perfB?.avgResponseTime || 1000);
    });

    // Create weighted list based on adaptive weights
    healthyBackends.forEach(backend => {
      const adaptiveWeight = this.adaptiveWeights.get(backend.id) || backend.weight;
      const effectiveWeight = Math.max(1, Math.round(adaptiveWeight));

      for (let i = 0; i < effectiveWeight; i++) {
        this.weightedList.push(backend);
      }

      // Initialize request count if not exists
      if (!this.requestCounts.has(backend.id)) {
        this.requestCounts.set(backend.id, 0);
      }
    });

    logger.debug(`Updated weighted list with ${this.weightedList.length} entries from ${healthyBackends.length} healthy backends`);
  }

  selectBackend(strategy = null) {
    const selectedStrategy = strategy || this.config.loadBalancer.strategy;

    // Update adaptive weights if needed
    this.updateAdaptiveWeights();

    if (this.weightedList.length === 0) {
      logger.error('No healthy backends available');
      return null;
    }

    let selectedBackend;

    switch (selectedStrategy) {
      case 'round-robin':
        selectedBackend = this.roundRobin();
        break;
      case 'weighted-round-robin':
        selectedBackend = this.weightedRoundRobin();
        break;
      case 'least-connections':
        selectedBackend = this.leastConnections();
        break;
      case 'priority':
        selectedBackend = this.priority();
        break;
      case 'random':
        selectedBackend = this.random();
        break;
      case 'performance-aware':
        selectedBackend = this.performanceAwareSelection();
        break;
      case 'adaptive-round-robin':
        selectedBackend = this.adaptiveRoundRobin();
        break;
      default:
        selectedBackend = this.adaptiveRoundRobin(); // Use adaptive as default
    }

    if (selectedBackend) {
      // Increment request count
      const currentCount = this.requestCounts.get(selectedBackend.id) || 0;
      this.requestCounts.set(selectedBackend.id, currentCount + 1);

      logger.debug(`Selected backend ${selectedBackend.id} using ${selectedStrategy} strategy`);
    }

    return selectedBackend;
  }

  roundRobin() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    if (healthyBackends.length === 0) return null;
    
    const backend = healthyBackends[this.currentIndex % healthyBackends.length];
    this.currentIndex++;
    return backend;
  }

  weightedRoundRobin() {
    if (this.weightedList.length === 0) return null;
    
    const backend = this.weightedList[this.currentIndex % this.weightedList.length];
    this.currentIndex++;
    return backend;
  }

  leastConnections() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    if (healthyBackends.length === 0) return null;
    
    let selectedBackend = healthyBackends[0];
    let minConnections = this.requestCounts.get(selectedBackend.id) || 0;
    
    for (const backend of healthyBackends) {
      const connections = this.requestCounts.get(backend.id) || 0;
      if (connections < minConnections) {
        minConnections = connections;
        selectedBackend = backend;
      }
    }
    
    return selectedBackend;
  }

  priority() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    if (healthyBackends.length === 0) return null;
    
    // Return the backend with the highest priority (lowest priority number)
    return healthyBackends.sort((a, b) => a.priority - b.priority)[0];
  }

  random() {
    if (this.weightedList.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * this.weightedList.length);
    return this.weightedList[randomIndex];
  }

  // New adaptive round robin that considers performance
  adaptiveRoundRobin() {
    if (this.weightedList.length === 0) return null;

    const backend = this.weightedList[this.currentIndex % this.weightedList.length];
    this.currentIndex++;
    return backend;
  }

  // Performance-aware selection prioritizes fastest backends
  performanceAwareSelection() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    if (healthyBackends.length === 0) return null;

    // Sort by performance score (combination of response time and success rate)
    const rankedBackends = healthyBackends.map(backend => {
      const perf = this.backendPerformance.get(backend.id);
      const responseTime = perf?.avgResponseTime || 1000;
      const successRate = perf?.successRate || 0.5;

      // Performance score: lower is better (faster response + higher success rate)
      const performanceScore = responseTime / successRate;

      return { backend, performanceScore };
    }).sort((a, b) => a.performanceScore - b.performanceScore);

    // Use weighted selection from top performers
    const topPerformers = rankedBackends.slice(0, Math.max(1, Math.ceil(rankedBackends.length * 0.7)));
    const selectedIndex = this.currentIndex % topPerformers.length;
    this.currentIndex++;

    return topPerformers[selectedIndex].backend;
  }

  // Get backend with failover support
  async selectBackendWithFailover(excludeBackends = []) {
    const healthyBackends = this.healthChecker.getHealthyBackends()
      .filter(backend => !excludeBackends.includes(backend.id));
    
    if (healthyBackends.length === 0) {
      logger.error('No healthy backends available for failover');
      return null;
    }

    // Create temporary weighted list excluding failed backends
    const tempWeightedList = [];
    healthyBackends.forEach(backend => {
      for (let i = 0; i < backend.weight; i++) {
        tempWeightedList.push(backend);
      }
    });

    if (tempWeightedList.length === 0) return null;

    // Use weighted round robin on remaining backends
    const index = this.currentIndex % tempWeightedList.length;
    this.currentIndex++;
    
    return tempWeightedList[index];
  }

  // Decrease request count when request completes
  releaseConnection(backendId) {
    const currentCount = this.requestCounts.get(backendId) || 0;
    if (currentCount > 0) {
      this.requestCounts.set(backendId, currentCount - 1);
    }
  }

  // Get load balancer statistics
  getStats() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    const totalRequests = Array.from(this.requestCounts.values()).reduce((sum, count) => sum + count, 0);
    
    return {
      strategy: this.config.loadBalancer.strategy,
      totalHealthyBackends: healthyBackends.length,
      totalRequests,
      requestDistribution: Object.fromEntries(this.requestCounts),
      weightedListSize: this.weightedList.length,
      backends: healthyBackends.map(backend => ({
        id: backend.id,
        url: backend.url,
        priority: backend.priority,
        weight: backend.weight,
        requests: this.requestCounts.get(backend.id) || 0,
        responseTime: backend.responseTime
      }))
    };
  }

  // Update adaptive weights based on performance metrics
  updateAdaptiveWeights() {
    const now = Date.now();
    if (now - this.lastWeightUpdate < this.weightUpdateInterval) {
      return; // Don't update too frequently
    }

    this.lastWeightUpdate = now;
    let weightsChanged = false;

    this.config.backends.forEach(backend => {
      const perf = this.backendPerformance.get(backend.id);
      if (!perf) return;

      const baseWeight = backend.weight;
      let newWeight = baseWeight;

      // Adjust weight based on performance
      if (perf.avgResponseTime > 0) {
        // Faster backends get higher weights
        const responseTimeFactor = Math.max(0.1, Math.min(2.0, 1000 / perf.avgResponseTime));
        const successRateFactor = Math.max(0.1, perf.successRate);

        newWeight = baseWeight * responseTimeFactor * successRateFactor;
        newWeight = Math.max(1, Math.min(baseWeight * 3, newWeight)); // Limit weight changes
      }

      const currentWeight = this.adaptiveWeights.get(backend.id);
      if (Math.abs(currentWeight - newWeight) > 1) {
        this.adaptiveWeights.set(backend.id, newWeight);
        weightsChanged = true;

        logger.debug(`Adjusted weight for backend ${backend.id}: ${currentWeight.toFixed(1)} -> ${newWeight.toFixed(1)}`);
      }
    });

    if (weightsChanged) {
      this.initializeWeightedList();
    }
  }

  // Start adaptive weight adjustment timer
  startAdaptiveWeightAdjustment() {
    setInterval(() => {
      this.updateAdaptiveWeights();
    }, this.weightUpdateInterval);
  }

  // Record backend performance metrics
  recordBackendPerformance(backendId, responseTime, success = true) {
    const perf = this.backendPerformance.get(backendId);
    if (!perf) return;

    const now = Date.now();

    // Add to recent requests (keep last 50)
    perf.recentRequests.push({
      timestamp: now,
      responseTime,
      success
    });

    if (perf.recentRequests.length > 50) {
      perf.recentRequests = perf.recentRequests.slice(-50);
    }

    // Calculate average response time from recent successful requests
    const recentSuccessful = perf.recentRequests.filter(req => req.success);
    if (recentSuccessful.length > 0) {
      perf.avgResponseTime = recentSuccessful.reduce((sum, req) => sum + req.responseTime, 0) / recentSuccessful.length;
    }

    // Calculate success rate from recent requests
    const recentTotal = perf.recentRequests.length;
    const recentSuccesses = perf.recentRequests.filter(req => req.success).length;
    perf.successRate = recentTotal > 0 ? recentSuccesses / recentTotal : 1.0;

    perf.lastUpdate = now;

    logger.debug(`Updated performance for backend ${backendId}: avgRT=${perf.avgResponseTime.toFixed(0)}ms, successRate=${(perf.successRate * 100).toFixed(1)}%`);
  }

  // Get backend performance metrics
  getBackendPerformance(backendId) {
    return this.backendPerformance.get(backendId);
  }

  // Reset statistics
  resetStats() {
    this.requestCounts.clear();
    this.currentIndex = 0;
    this.responseTimeHistory.clear();
    this.backendPerformance.clear();
    this.adaptiveWeights.clear();
    this.initializeBackendMetrics();
    logger.info('Load balancer statistics reset');
  }
}

module.exports = LoadBalancer;
