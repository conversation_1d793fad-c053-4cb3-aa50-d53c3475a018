const logger = require('./logger');

class LoadBalancer {
  constructor(config, healthChecker) {
    this.config = config;
    this.healthChecker = healthChecker;
    this.currentIndex = 0;
    this.weightedList = [];
    this.requestCounts = new Map();
    
    this.initializeWeightedList();
    
    // Listen for health changes to update weighted list
    this.healthChecker.on('healthChange', () => {
      this.initializeWeightedList();
    });
  }

  initializeWeightedList() {
    this.weightedList = [];
    const healthyBackends = this.healthChecker.getHealthyBackends();
    
    // Sort by priority (lower number = higher priority)
    healthyBackends.sort((a, b) => a.priority - b.priority);
    
    // Create weighted list based on weights
    healthyBackends.forEach(backend => {
      for (let i = 0; i < backend.weight; i++) {
        this.weightedList.push(backend);
      }
      
      // Initialize request count if not exists
      if (!this.requestCounts.has(backend.id)) {
        this.requestCounts.set(backend.id, 0);
      }
    });

    logger.debug(`Updated weighted list with ${this.weightedList.length} entries from ${healthyBackends.length} healthy backends`);
  }

  selectBackend(strategy = null) {
    const selectedStrategy = strategy || this.config.loadBalancer.strategy;
    
    if (this.weightedList.length === 0) {
      logger.error('No healthy backends available');
      return null;
    }

    let selectedBackend;

    switch (selectedStrategy) {
      case 'round-robin':
        selectedBackend = this.roundRobin();
        break;
      case 'weighted-round-robin':
        selectedBackend = this.weightedRoundRobin();
        break;
      case 'least-connections':
        selectedBackend = this.leastConnections();
        break;
      case 'priority':
        selectedBackend = this.priority();
        break;
      case 'random':
        selectedBackend = this.random();
        break;
      default:
        selectedBackend = this.weightedRoundRobin();
    }

    if (selectedBackend) {
      // Increment request count
      const currentCount = this.requestCounts.get(selectedBackend.id) || 0;
      this.requestCounts.set(selectedBackend.id, currentCount + 1);
      
      logger.debug(`Selected backend ${selectedBackend.id} using ${selectedStrategy} strategy`);
    }

    return selectedBackend;
  }

  roundRobin() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    if (healthyBackends.length === 0) return null;
    
    const backend = healthyBackends[this.currentIndex % healthyBackends.length];
    this.currentIndex++;
    return backend;
  }

  weightedRoundRobin() {
    if (this.weightedList.length === 0) return null;
    
    const backend = this.weightedList[this.currentIndex % this.weightedList.length];
    this.currentIndex++;
    return backend;
  }

  leastConnections() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    if (healthyBackends.length === 0) return null;
    
    let selectedBackend = healthyBackends[0];
    let minConnections = this.requestCounts.get(selectedBackend.id) || 0;
    
    for (const backend of healthyBackends) {
      const connections = this.requestCounts.get(backend.id) || 0;
      if (connections < minConnections) {
        minConnections = connections;
        selectedBackend = backend;
      }
    }
    
    return selectedBackend;
  }

  priority() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    if (healthyBackends.length === 0) return null;
    
    // Return the backend with the highest priority (lowest priority number)
    return healthyBackends.sort((a, b) => a.priority - b.priority)[0];
  }

  random() {
    if (this.weightedList.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * this.weightedList.length);
    return this.weightedList[randomIndex];
  }

  // Get backend with failover support
  async selectBackendWithFailover(excludeBackends = []) {
    const healthyBackends = this.healthChecker.getHealthyBackends()
      .filter(backend => !excludeBackends.includes(backend.id));
    
    if (healthyBackends.length === 0) {
      logger.error('No healthy backends available for failover');
      return null;
    }

    // Create temporary weighted list excluding failed backends
    const tempWeightedList = [];
    healthyBackends.forEach(backend => {
      for (let i = 0; i < backend.weight; i++) {
        tempWeightedList.push(backend);
      }
    });

    if (tempWeightedList.length === 0) return null;

    // Use weighted round robin on remaining backends
    const index = this.currentIndex % tempWeightedList.length;
    this.currentIndex++;
    
    return tempWeightedList[index];
  }

  // Decrease request count when request completes
  releaseConnection(backendId) {
    const currentCount = this.requestCounts.get(backendId) || 0;
    if (currentCount > 0) {
      this.requestCounts.set(backendId, currentCount - 1);
    }
  }

  // Get load balancer statistics
  getStats() {
    const healthyBackends = this.healthChecker.getHealthyBackends();
    const totalRequests = Array.from(this.requestCounts.values()).reduce((sum, count) => sum + count, 0);
    
    return {
      strategy: this.config.loadBalancer.strategy,
      totalHealthyBackends: healthyBackends.length,
      totalRequests,
      requestDistribution: Object.fromEntries(this.requestCounts),
      weightedListSize: this.weightedList.length,
      backends: healthyBackends.map(backend => ({
        id: backend.id,
        url: backend.url,
        priority: backend.priority,
        weight: backend.weight,
        requests: this.requestCounts.get(backend.id) || 0,
        responseTime: backend.responseTime
      }))
    };
  }

  // Reset statistics
  resetStats() {
    this.requestCounts.clear();
    this.currentIndex = 0;
    logger.info('Load balancer statistics reset');
  }
}

module.exports = LoadBalancer;
