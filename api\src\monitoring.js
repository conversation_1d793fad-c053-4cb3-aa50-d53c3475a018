const EventEmitter = require('events');
const logger = require('./logger');

class MonitoringService extends EventEmitter {
  constructor(config) {
    super();
    this.config = config;
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byStatus: {},
        byBackend: {},
        responseTimeSum: 0,
        responseTimeCount: 0
      },
      backends: {},
      system: {
        startTime: Date.now(),
        lastHealthCheck: null
      },
      errors: {
        total: 0,
        byType: {},
        recent: []
      }
    };
    
    this.startSystemMonitoring();
  }

  startSystemMonitoring() {
    // Collect system metrics every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Clean up old error logs every 5 minutes
    setInterval(() => {
      this.cleanupOldErrors();
    }, 300000);
  }

  collectSystemMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    this.emit('systemMetrics', {
      timestamp: Date.now(),
      memory: {
        rss: memUsage.rss,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: process.uptime()
    });
  }

  recordRequest(req, res, responseTime, backend, error = null) {
    this.metrics.requests.total++;
    
    if (error) {
      this.metrics.requests.failed++;
      this.recordError(error, { url: req.url, method: req.method, backend: backend?.id });
    } else {
      this.metrics.requests.successful++;
    }

    // Record status code
    const statusCode = res.statusCode || (error ? 500 : 200);
    this.metrics.requests.byStatus[statusCode] = (this.metrics.requests.byStatus[statusCode] || 0) + 1;

    // Record backend usage
    if (backend) {
      if (!this.metrics.requests.byBackend[backend.id]) {
        this.metrics.requests.byBackend[backend.id] = {
          total: 0,
          successful: 0,
          failed: 0,
          responseTimeSum: 0,
          responseTimeCount: 0
        };
      }
      
      const backendMetrics = this.metrics.requests.byBackend[backend.id];
      backendMetrics.total++;
      
      if (error) {
        backendMetrics.failed++;
      } else {
        backendMetrics.successful++;
      }

      if (responseTime) {
        backendMetrics.responseTimeSum += responseTime;
        backendMetrics.responseTimeCount++;
        
        this.metrics.requests.responseTimeSum += responseTime;
        this.metrics.requests.responseTimeCount++;
      }
    }

    // Emit request event for real-time monitoring
    this.emit('request', {
      timestamp: Date.now(),
      method: req.method,
      url: req.url,
      statusCode,
      responseTime,
      backend: backend?.id,
      error: error?.message,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
  }

  recordError(error, context = {}) {
    this.metrics.errors.total++;
    
    const errorType = error.constructor.name;
    this.metrics.errors.byType[errorType] = (this.metrics.errors.byType[errorType] || 0) + 1;
    
    // Store recent errors (keep last 100)
    this.metrics.errors.recent.push({
      timestamp: Date.now(),
      message: error.message,
      type: errorType,
      stack: error.stack,
      context
    });
    
    if (this.metrics.errors.recent.length > 100) {
      this.metrics.errors.recent = this.metrics.errors.recent.slice(-100);
    }

    // Emit error event
    this.emit('error', {
      timestamp: Date.now(),
      error: {
        message: error.message,
        type: errorType,
        stack: error.stack
      },
      context
    });

    logger.logError(error, context);
  }

  recordBackendHealth(backendId, isHealthy, responseTime, error = null) {
    if (!this.metrics.backends[backendId]) {
      this.metrics.backends[backendId] = {
        healthChecks: 0,
        healthyChecks: 0,
        unhealthyChecks: 0,
        lastHealthCheck: null,
        lastResponseTime: null,
        averageResponseTime: 0,
        responseTimeSum: 0,
        responseTimeCount: 0
      };
    }

    const backendMetrics = this.metrics.backends[backendId];
    backendMetrics.healthChecks++;
    backendMetrics.lastHealthCheck = Date.now();

    if (isHealthy) {
      backendMetrics.healthyChecks++;
      if (responseTime) {
        backendMetrics.lastResponseTime = responseTime;
        backendMetrics.responseTimeSum += responseTime;
        backendMetrics.responseTimeCount++;
        backendMetrics.averageResponseTime = backendMetrics.responseTimeSum / backendMetrics.responseTimeCount;
      }
    } else {
      backendMetrics.unhealthyChecks++;
      if (error) {
        this.recordError(error, { backendId, type: 'healthCheck' });
      }
    }

    // Emit backend health event
    this.emit('backendHealth', {
      timestamp: Date.now(),
      backendId,
      isHealthy,
      responseTime,
      error: error?.message
    });
  }

  cleanupOldErrors() {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    this.metrics.errors.recent = this.metrics.errors.recent.filter(
      error => error.timestamp > oneHourAgo
    );
  }

  getMetrics() {
    const now = Date.now();
    const uptime = now - this.metrics.system.startTime;
    
    // Calculate average response times
    const avgResponseTime = this.metrics.requests.responseTimeCount > 0 
      ? this.metrics.requests.responseTimeSum / this.metrics.requests.responseTimeCount 
      : 0;

    const backendStats = {};
    Object.keys(this.metrics.backends).forEach(backendId => {
      const backend = this.metrics.backends[backendId];
      backendStats[backendId] = {
        ...backend,
        healthRate: backend.healthChecks > 0 ? backend.healthyChecks / backend.healthChecks : 0,
        averageResponseTime: backend.averageResponseTime
      };
    });

    const requestStats = {};
    Object.keys(this.metrics.requests.byBackend).forEach(backendId => {
      const backend = this.metrics.requests.byBackend[backendId];
      requestStats[backendId] = {
        ...backend,
        successRate: backend.total > 0 ? backend.successful / backend.total : 0,
        averageResponseTime: backend.responseTimeCount > 0 
          ? backend.responseTimeSum / backend.responseTimeCount 
          : 0
      };
    });

    return {
      timestamp: now,
      uptime,
      requests: {
        ...this.metrics.requests,
        successRate: this.metrics.requests.total > 0 
          ? this.metrics.requests.successful / this.metrics.requests.total 
          : 0,
        averageResponseTime: avgResponseTime,
        byBackend: requestStats
      },
      backends: backendStats,
      errors: {
        total: this.metrics.errors.total,
        byType: this.metrics.errors.byType,
        recentCount: this.metrics.errors.recent.length
      },
      system: {
        uptime,
        startTime: this.metrics.system.startTime,
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      }
    };
  }

  getRecentErrors(limit = 50) {
    return this.metrics.errors.recent
      .slice(-limit)
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  resetMetrics() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byStatus: {},
        byBackend: {},
        responseTimeSum: 0,
        responseTimeCount: 0
      },
      backends: {},
      system: {
        startTime: Date.now(),
        lastHealthCheck: null
      },
      errors: {
        total: 0,
        byType: {},
        recent: []
      }
    };
    
    logger.info('Monitoring metrics reset');
  }

  // Get alerts based on thresholds
  getAlerts() {
    const alerts = [];
    const metrics = this.getMetrics();
    
    // High error rate alert
    if (metrics.requests.total > 100 && metrics.requests.successRate < 0.95) {
      alerts.push({
        type: 'high_error_rate',
        severity: 'warning',
        message: `Success rate is ${(metrics.requests.successRate * 100).toFixed(2)}%`,
        timestamp: Date.now()
      });
    }

    // Slow response time alert
    if (metrics.requests.averageResponseTime > 5000) {
      alerts.push({
        type: 'slow_response',
        severity: 'warning',
        message: `Average response time is ${metrics.requests.averageResponseTime.toFixed(0)}ms`,
        timestamp: Date.now()
      });
    }

    // Backend health alerts
    Object.keys(metrics.backends).forEach(backendId => {
      const backend = metrics.backends[backendId];
      if (backend.healthChecks > 10 && backend.healthRate < 0.8) {
        alerts.push({
          type: 'backend_unhealthy',
          severity: 'critical',
          message: `Backend ${backendId} health rate is ${(backend.healthRate * 100).toFixed(2)}%`,
          timestamp: Date.now()
        });
      }
    });

    return alerts;
  }
}

module.exports = MonitoringService;
