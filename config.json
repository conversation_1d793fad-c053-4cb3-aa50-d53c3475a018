{"server": {"port": 3000, "host": "0.0.0.0"}, "backends": [{"id": "api-fast-ai", "url": "https://api.fast-ai.xyz", "priority": 1, "weight": 100, "maxRetries": 3, "timeout": 5000, "healthCheck": {"enabled": true, "path": "/api/v1/guest/comm/config", "interval": 30000, "timeout": 3000, "retries": 2}}, {"id": "get-fast-ai", "url": "https://get.fast-ai.xyz", "priority": 2, "weight": 80, "maxRetries": 3, "timeout": 5000, "healthCheck": {"enabled": true, "path": "/api/v1/guest/comm/config", "interval": 30000, "timeout": 3000, "retries": 2}}, {"id": "get-source-ai", "url": "https://source.20006666.xyz", "priority": 3, "weight": 80, "maxRetries": 3, "timeout": 5000, "healthCheck": {"enabled": true, "path": "/api/v1/guest/comm/config", "interval": 30000, "timeout": 3000, "retries": 2}}], "loadBalancer": {"strategy": "weighted-round-robin", "failoverEnabled": true, "circuitBreaker": {"enabled": true, "failureThreshold": 5, "resetTimeout": 60000, "monitoringPeriod": 10000}}, "cache": {"enabled": true, "ttl": 300000, "maxSize": 1000}, "rateLimit": {"windowMs": 60000, "max": 1000, "message": "Too many requests from this IP"}, "logging": {"level": "info", "format": "json", "file": {"enabled": true, "filename": "logs/bff.log", "maxSize": "10m", "maxFiles": 5}}, "monitoring": {"enabled": true, "metricsPath": "/metrics", "healthPath": "/health"}}