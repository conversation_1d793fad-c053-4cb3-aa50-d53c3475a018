#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Netlify Deployment Fix Script');
console.log('═'.repeat(40));

// Check and fix common issues
const fixes = [];

// 1. Check .nvmrc file
if (!fs.existsSync('.nvmrc')) {
  fs.writeFileSync('.nvmrc', '18.20.8');
  fixes.push('✅ Created .nvmrc file with Node.js 18.20.8');
} else {
  const nvmrcContent = fs.readFileSync('.nvmrc', 'utf8').trim();
  if (!nvmrcContent.startsWith('18')) {
    fs.writeFileSync('.nvmrc', '18.20.8');
    fixes.push('✅ Updated .nvmrc to Node.js 18.20.8');
  }
}

// 2. Check package.json dependencies
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

// Move serverless-http to dependencies if it's in devDependencies
if (packageJson.devDependencies && packageJson.devDependencies['serverless-http']) {
  if (!packageJson.dependencies) packageJson.dependencies = {};
  packageJson.dependencies['serverless-http'] = packageJson.devDependencies['serverless-http'];
  delete packageJson.devDependencies['serverless-http'];
  
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  fixes.push('✅ Moved serverless-http to dependencies');
}

// Update engines
if (!packageJson.engines || packageJson.engines.node !== '>=18.0.0') {
  packageJson.engines = {
    node: '>=18.0.0',
    npm: '>=9.0.0'
  };
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  fixes.push('✅ Updated Node.js engine requirement');
}

// 3. Check netlify.toml
const netlifyTomlPath = 'netlify.toml';
if (fs.existsSync(netlifyTomlPath)) {
  let netlifyToml = fs.readFileSync(netlifyTomlPath, 'utf8');
  
  // Fix build command
  if (!netlifyToml.includes('npm ci')) {
    netlifyToml = netlifyToml.replace(
      /command = "npm run build"/,
      'command = "npm ci && npm run build"'
    );
    fs.writeFileSync(netlifyTomlPath, netlifyToml);
    fixes.push('✅ Updated Netlify build command to use npm ci');
  }
  
  // Fix Node version
  if (netlifyToml.includes('NODE_VERSION = "18.20.8"')) {
    netlifyToml = netlifyToml.replace(
      /NODE_VERSION = "18.20.8"/,
      'NODE_VERSION = "18"'
    );
    fs.writeFileSync(netlifyTomlPath, netlifyToml);
    fixes.push('✅ Simplified Node version in netlify.toml');
  }
}

// 4. Check Netlify functions directory
const functionsDir = 'netlify/functions';
if (!fs.existsSync(functionsDir)) {
  fs.mkdirSync(functionsDir, { recursive: true });
  fixes.push('✅ Created netlify/functions directory');
}

// 5. Validate Netlify function
const apiFunctionPath = path.join(functionsDir, 'api.js');
if (fs.existsSync(apiFunctionPath)) {
  const functionContent = fs.readFileSync(apiFunctionPath, 'utf8');
  if (!functionContent.includes('process.env.NETLIFY = \'true\'')) {
    fixes.push('⚠️  Netlify function may need environment variable fix');
  }
}

// 6. Check for package-lock.json
if (!fs.existsSync('package-lock.json')) {
  fixes.push('⚠️  No package-lock.json found - run "npm install" to generate it');
}

// Display results
console.log('\n📋 Fix Results:');
if (fixes.length === 0) {
  console.log('✅ No issues found - your Netlify configuration looks good!');
} else {
  fixes.forEach(fix => console.log(`   ${fix}`));
}

// Recommendations
console.log('\n💡 Next Steps:');
console.log('   1. Commit the changes made by this script');
console.log('   2. Push to your repository');
console.log('   3. Trigger a new Netlify deploy');
console.log('   4. If build still fails, check NETLIFY-TROUBLESHOOTING.md');

// Test commands
console.log('\n🧪 Test Commands:');
console.log('   npm run test:connectivity    # Test backend connectivity');
console.log('   netlify dev                  # Test locally');
console.log('   netlify deploy               # Deploy to preview');
console.log('   netlify deploy --prod        # Deploy to production');

console.log('\n🔗 Useful Links:');
console.log('   Netlify Dashboard: https://app.netlify.com/');
console.log('   Build Logs: Check your site\'s deploy logs');
console.log('   Function Logs: Functions tab in Netlify dashboard');

console.log('\n✨ Done!');
