[build]
  # Build command for the frontend
  command = "npm ci && npm run build"
  # Directory to publish (Vite output)
  publish = "dist"
  # Functions directory
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  NPM_USE_PRODUCTION = "false"

# Redirect all API calls to the serverless function
[[redirects]]
  from = "/health"
  to = "/.netlify/functions/api"
  status = 200

# Fallback health check
[[redirects]]
  from = "/health-simple"
  to = "/.netlify/functions/health"
  status = 200

[[redirects]]
  from = "/status"
  to = "/.netlify/functions/api"
  status = 200

[[redirects]]
  from = "/metrics"
  to = "/.netlify/functions/api"
  status = 200

[[redirects]]
  from = "/alerts"
  to = "/.netlify/functions/api"
  status = 200

[[redirects]]
  from = "/errors"
  to = "/.netlify/functions/api"
  status = 200

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/api"
  status = 200

# Catch-all redirect for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Headers for API endpoints
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

# Environment variables (set these in Netlify dashboard)
# HEALTH_CHECK_TIMEOUT_OVERRIDE=15000
# LOG_LEVEL=info
# NODE_ENV=production
