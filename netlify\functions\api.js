// Set Netlify environment variable early
process.env.NETLIFY = 'true';

let serverless, app;

try {
  serverless = require('serverless-http');
  app = require('../../api/server');
} catch (error) {
  console.error('Failed to load dependencies:', error);
  // Fallback to basic functionality
}

// Create serverless handler for Netlify
const handler = app ? serverless(app, {
  // Configure binary media types if needed
  binary: ['image/*', 'application/pdf', 'application/octet-stream']
}) : null;

// Export the handler
exports.handler = async (event, context) => {
  // If dependencies failed to load, return a basic response
  if (!handler) {
    return {
      statusCode: 503,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        error: 'Service Unavailable',
        message: 'BFF dependencies not loaded',
        timestamp: new Date().toISOString(),
        environment: 'netlify'
      })
    };
  }

  // Add timeout handling for Netlify (max 10 seconds for free tier)
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Function timeout')), 9000);
  });

  try {
    // Race between the actual handler and timeout
    const result = await Promise.race([
      handler(event, context),
      timeoutPromise
    ]);

    return result;
  } catch (error) {
    console.error('Netlify function error:', error);

    // Return error response
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: error.message,
        timestamp: new Date().toISOString(),
        environment: 'netlify'
      })
    };
  }
};
