// Simple health check function for Netlify
// This is a fallback in case the main API function has issues

exports.handler = async (event, context) => {
  // Set environment
  process.env.NETLIFY = 'true';
  
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  };

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Simple health check without full BFF functionality
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: 'netlify',
      version: '1.0.0',
      note: 'Simplified health check - full BFF functionality may not be available',
      backends: {
        'api-fast-ai': {
          id: 'api-fast-ai',
          url: 'https://api.fast-ai.xyz',
          status: 'unknown',
          isHealthy: false,
          note: 'Health check not performed in simplified mode'
        },
        'get-fast-ai': {
          id: 'get-fast-ai',
          url: 'https://get.fast-ai.xyz',
          status: 'unknown',
          isHealthy: false,
          note: 'Health check not performed in simplified mode'
        }
      }
    };

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(healthData)
    };

  } catch (error) {
    console.error('Health check error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: error.message,
        timestamp: new Date().toISOString(),
        environment: 'netlify'
      })
    };
  }
};
