{"name": "high-speed-bff", "version": "1.0.0", "description": "High-speed Backend for Frontend with health checking and load balancing", "main": "server.js", "scripts": {"dev": "vite", "build": "vite build", "build:netlify": "npm run build", "build:workers": "cp workers/simple-adapter.js dist/worker.js", "start": "node server.js", "start:api": "node api/server.js", "test": "jest", "test:health": "node test-health-check.js", "test:connectivity": "node test-backend-connectivity.js", "test:local": "node test-local-health.js", "test:deployment": "node test-deployment.js", "test:multi-platform": "node test-multi-platform.js", "fix:netlify": "node fix-netlify.js", "deploy:netlify": "netlify deploy --prod", "deploy:workers": "wrangler publish"}, "keywords": ["bff", "backend-for-frontend", "load-balancer", "health-check", "high-performance"], "author": "", "license": "MIT", "dependencies": {"@vitejs/plugin-react": "^4.6.0", "axios": "^1.6.0", "compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "http-proxy-middleware": "^2.0.6", "node-cron": "^3.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "serverless-http": "^3.2.0", "vite": "^7.0.4", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3", "wrangler": "^3.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}