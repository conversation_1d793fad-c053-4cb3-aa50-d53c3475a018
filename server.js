#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const BFFServer = require('./api/server');

// ASCII Art Banner
const banner = `
╔══════════════════════════════════════════════════════════════╗
║                    High-Speed BFF Server                     ║
║              Backend for Frontend with Load Balancing        ║
╚══════════════════════════════════════════════════════════════╝
`;

console.log(banner);

// Check if config file exists
const configPath = path.join(__dirname, 'config.json');
if (!fs.existsSync(configPath)) {
  console.error('❌ Error: config.json not found!');
  console.log('📝 Please create a config.json file with your backend configuration.');
  console.log('📖 See README.md for configuration examples.');
  process.exit(1);
}

// Validate Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.error(`❌ Error: Node.js ${nodeVersion} is not supported.`);
  console.log('📦 Please upgrade to Node.js 16 or higher.');
  process.exit(1);
}

// Load and validate configuration
let config;
try {
  config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
} catch (error) {
  console.error('❌ Error: Invalid config.json file!');
  console.error(error.message);
  process.exit(1);
}

// Validate required configuration
if (!config.backends || !Array.isArray(config.backends) || config.backends.length === 0) {
  console.error('❌ Error: No backends configured!');
  console.log('📝 Please add at least one backend to your config.json file.');
  process.exit(1);
}

// Validate backend configuration
for (const backend of config.backends) {
  if (!backend.id || !backend.url) {
    console.error(`❌ Error: Backend missing required fields (id, url): ${JSON.stringify(backend)}`);
    process.exit(1);
  }
  
  try {
    new URL(backend.url);
  } catch (error) {
    console.error(`❌ Error: Invalid backend URL: ${backend.url}`);
    process.exit(1);
  }
}

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('📁 Created logs directory');
}

// Display configuration summary
console.log('🔧 Configuration Summary:');
console.log(`   📡 Server: ${config.server?.host || '0.0.0.0'}:${config.server?.port || 3000}`);
console.log(`   🎯 Backends: ${config.backends.length}`);
config.backends.forEach((backend, index) => {
  console.log(`      ${index + 1}. ${backend.id} (${backend.url}) - Priority: ${backend.priority || 1}, Weight: ${backend.weight || 100}`);
});
console.log(`   ⚖️  Load Balancer: ${config.loadBalancer?.strategy || 'weighted-round-robin'}`);
console.log(`   💾 Cache: ${config.cache?.enabled ? 'Enabled' : 'Disabled'}`);
console.log(`   🛡️  Rate Limit: ${config.rateLimit?.max || 1000} requests per ${(config.rateLimit?.windowMs || 60000) / 1000}s`);

// Environment information
console.log('\n🌍 Environment:');
console.log(`   📦 Node.js: ${process.version}`);
console.log(`   🖥️  Platform: ${process.platform}`);
console.log(`   🏠 Working Directory: ${process.cwd()}`);
console.log(`   🔧 Environment: ${process.env.NODE_ENV || 'development'}`);

// Start the server
console.log('\n🚀 Starting BFF Server...\n');

try {
  const bffServer = new BFFServer();
  bffServer.start();
  
  // Display helpful information after startup
  setTimeout(() => {
    console.log('\n✅ Server started successfully!');
    console.log('\n📊 Available Endpoints:');
    console.log(`   🏥 Health Check: http://localhost:${config.server?.port || 3000}/health`);
    console.log(`   📈 Metrics: http://localhost:${config.server?.port || 3000}/metrics`);
    console.log(`   🚨 Alerts: http://localhost:${config.server?.port || 3000}/alerts`);
    console.log(`   🐛 Errors: http://localhost:${config.server?.port || 3000}/errors`);
    console.log(`   🔌 API Proxy: http://localhost:${config.server?.port || 3000}/api/*`);
    
    console.log('\n💡 Tips:');
    console.log('   • Monitor backend health at /health endpoint');
    console.log('   • View detailed metrics at /metrics endpoint');
    console.log('   • Check logs in the logs/ directory');
    console.log('   • Use Ctrl+C to gracefully shutdown');
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('\n⚠️  Development Mode:');
      console.log('   • Detailed console logging enabled');
      console.log('   • Error stack traces included in responses');
      console.log('   • Set NODE_ENV=production for production deployment');
    }
  }, 2000);
  
} catch (error) {
  console.error('\n❌ Failed to start server:');
  console.error(error.message);
  
  if (error.code === 'EADDRINUSE') {
    console.log(`\n💡 Port ${config.server?.port || 3000} is already in use.`);
    console.log('   Try changing the port in config.json or stopping the other process.');
  } else if (error.code === 'EACCES') {
    console.log(`\n💡 Permission denied for port ${config.server?.port || 3000}.`);
    console.log('   Try using a port number above 1024 or run with elevated privileges.');
  }
  
  process.exit(1);
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('\n💥 Uncaught Exception:', error.message);
  console.error(error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('\n💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
});

process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT (Ctrl+C), shutting down gracefully...');
});
