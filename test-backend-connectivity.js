#!/usr/bin/env node

const axios = require('axios');
const config = require('./config.json');

async function testBackendConnectivity(backend) {
  console.log(`\n🔍 Testing backend: ${backend.id}`);
  console.log(`   🌐 URL: ${backend.url}`);
  console.log(`   🏥 Health path: ${backend.healthCheck.path}`);
  
  const healthUrl = `${backend.url}${backend.healthCheck.path}`;
  console.log(`   📡 Full health URL: ${healthUrl}`);
  
  const tests = [
    {
      name: 'Basic connectivity',
      test: async () => {
        const response = await axios.get(backend.url, {
          timeout: 5000,
          validateStatus: () => true
        });
        return { status: response.status, data: response.data };
      }
    },
    {
      name: 'Health endpoint (normal timeout)',
      test: async () => {
        const response = await axios.get(healthUrl, {
          timeout: backend.healthCheck.timeout,
          validateStatus: () => true,
          headers: {
            'User-Agent': 'BFF-HealthChecker/1.0',
            'Accept': 'application/json'
          }
        });
        return { status: response.status, data: response.data };
      }
    },
    {
      name: 'Health endpoint (extended timeout)',
      test: async () => {
        const response = await axios.get(healthUrl, {
          timeout: 15000, // Extended timeout like Vercel
          validateStatus: () => true,
          headers: {
            'User-Agent': 'BFF-HealthChecker/1.0',
            'Accept': 'application/json'
          }
        });
        return { status: response.status, data: response.data };
      }
    }
  ];
  
  const results = [];
  
  for (const test of tests) {
    console.log(`\n   🧪 ${test.name}...`);
    
    try {
      const startTime = Date.now();
      const result = await test.test();
      const responseTime = Date.now() - startTime;
      
      console.log(`      ✅ Status: ${result.status} (${responseTime}ms)`);
      
      if (result.data) {
        if (typeof result.data === 'object') {
          const keys = Object.keys(result.data);
          console.log(`      📊 Response keys: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`);
          console.log(`      📏 Data size: ${JSON.stringify(result.data).length} bytes`);
          
          // Check if it's valid config data
          const hasValidConfig = result.data && (
            typeof result.data === 'object' &&
            (result.data.data || result.data.config || Object.keys(result.data).length > 0)
          );
          console.log(`      ✅ Valid config: ${hasValidConfig ? 'Yes' : 'No'}`);
        } else {
          console.log(`      📝 Response: ${result.data.toString().substring(0, 100)}...`);
        }
      } else {
        console.log(`      ⚠️  Empty response`);
      }
      
      results.push({
        test: test.name,
        success: true,
        status: result.status,
        responseTime,
        hasData: !!result.data
      });
      
    } catch (error) {
      console.log(`      ❌ Error: ${error.message}`);
      
      if (error.code === 'ENOTFOUND') {
        console.log(`      🌐 DNS resolution failed`);
      } else if (error.code === 'ECONNREFUSED') {
        console.log(`      🔌 Connection refused`);
      } else if (error.code === 'ETIMEDOUT') {
        console.log(`      ⏰ Request timed out`);
      } else if (error.code === 'ECONNRESET') {
        console.log(`      🔄 Connection reset`);
      } else if (error.response) {
        console.log(`      📄 HTTP ${error.response.status}: ${error.response.statusText}`);
      }
      
      results.push({
        test: test.name,
        success: false,
        error: error.message,
        code: error.code
      });
    }
  }
  
  return results;
}

async function runConnectivityTests() {
  console.log('🌐 Backend Connectivity Test Suite');
  console.log('═'.repeat(50));
  
  const allResults = [];
  
  for (const backend of config.backends) {
    const results = await testBackendConnectivity(backend);
    allResults.push({
      backend: backend.id,
      url: backend.url,
      results
    });
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('═'.repeat(50));
  
  allResults.forEach(backendResult => {
    console.log(`\n🎯 ${backendResult.backend} (${backendResult.url}):`);
    
    const successful = backendResult.results.filter(r => r.success);
    const failed = backendResult.results.filter(r => !r.success);
    
    console.log(`   ✅ Successful: ${successful.length}/${backendResult.results.length}`);
    console.log(`   ❌ Failed: ${failed.length}/${backendResult.results.length}`);
    
    if (failed.length > 0) {
      console.log(`   🔍 Failed tests: ${failed.map(f => f.test).join(', ')}`);
    }
  });
  
  // Overall recommendations
  console.log('\n💡 Recommendations:');
  
  const allBackendsHealthy = allResults.every(br => 
    br.results.some(r => r.test.includes('Health endpoint') && r.success)
  );
  
  if (allBackendsHealthy) {
    console.log('   🎉 All backends are reachable! The issue might be in the BFF configuration.');
  } else {
    console.log('   ⚠️  Some backends are not reachable. This explains the "No healthy backends" error.');
    console.log('   🔧 Check:');
    console.log('      • Network connectivity from your deployment environment');
    console.log('      • Backend server status');
    console.log('      • Firewall or security group settings');
    console.log('      • DNS resolution');
  }
  
  console.log('\n🚀 Next Steps:');
  console.log('   1. If all tests pass here but Vercel fails, it\'s a Vercel networking issue');
  console.log('   2. If tests fail here too, fix the backend connectivity first');
  console.log('   3. Check Vercel function logs for detailed error messages');
  
  return allResults;
}

runConnectivityTests().catch(console.error);
