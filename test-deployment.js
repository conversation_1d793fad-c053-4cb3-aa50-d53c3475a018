#!/usr/bin/env node

const axios = require('axios');

// Replace with your actual Vercel deployment URL
const BASE_URL = process.argv[2] || 'https://your-deployment-url.vercel.app';

async function testEndpoint(path, description) {
  console.log(`\n🔍 Testing ${description}...`);
  console.log(`   📡 URL: ${BASE_URL}${path}`);
  
  try {
    const startTime = Date.now();
    const response = await axios.get(`${BASE_URL}${path}`, {
      timeout: 15000,
      validateStatus: () => true // Accept any status code
    });
    const responseTime = Date.now() - startTime;
    
    console.log(`   ✅ Status: ${response.status} (${responseTime}ms)`);
    console.log(`   📄 Content-Type: ${response.headers['content-type']}`);
    
    if (response.data) {
      if (typeof response.data === 'object') {
        console.log(`   📊 Response keys: ${Object.keys(response.data).join(', ')}`);
        
        // Show specific info for health endpoint
        if (path === '/health' && response.data.status) {
          console.log(`   🏥 Health Status: ${response.data.status}`);
          if (response.data.backends) {
            const backendCount = Object.keys(response.data.backends).length;
            console.log(`   🎯 Backends: ${backendCount} configured`);
          }
        }
      } else {
        console.log(`   📝 Response: ${response.data.toString().substring(0, 100)}...`);
      }
    } else {
      console.log(`   ⚠️  Empty response body`);
    }
    
    return {
      success: response.status >= 200 && response.status < 400,
      status: response.status,
      responseTime,
      hasData: !!response.data
    };
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    
    if (error.code === 'ENOTFOUND') {
      console.log(`   🌐 DNS resolution failed - check the URL`);
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`   🔌 Connection refused`);
    } else if (error.code === 'ETIMEDOUT') {
      console.log(`   ⏰ Request timed out`);
    } else if (error.response) {
      console.log(`   📄 HTTP ${error.response.status}: ${error.response.statusText}`);
    }
    
    return {
      success: false,
      error: error.message,
      code: error.code
    };
  }
}

async function runDeploymentTests() {
  console.log('🚀 BFF Deployment Test Suite');
  console.log(`🌐 Testing deployment at: ${BASE_URL}\n`);
  
  const tests = [
    { path: '/status', description: 'Basic Status Endpoint' },
    { path: '/health', description: 'Health Check Endpoint' },
    { path: '/metrics', description: 'Metrics Endpoint' },
    { path: '/alerts', description: 'Alerts Endpoint' }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testEndpoint(test.path, test.description);
    results.push({
      ...test,
      ...result
    });
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('═'.repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (failed.length > 0) {
    console.log('\n❌ Failed Tests:');
    failed.forEach(test => {
      console.log(`   • ${test.description}: ${test.error || `HTTP ${test.status}`}`);
    });
  }
  
  if (successful.length > 0) {
    console.log('\n✅ Successful Tests:');
    successful.forEach(test => {
      console.log(`   • ${test.description}: HTTP ${test.status} (${test.responseTime}ms)`);
    });
  }
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  
  if (failed.length === 0) {
    console.log('   🎉 All tests passed! Your BFF deployment is working correctly.');
  } else if (failed.some(t => t.path === '/status')) {
    console.log('   ⚠️  Basic status endpoint failed - check Vercel deployment logs');
  } else if (failed.some(t => t.path === '/health')) {
    console.log('   ⚠️  Health endpoint failed - this might be the issue you reported');
    console.log('   🔧 Check backend connectivity and health check configuration');
  }
  
  console.log('\n🔗 Next Steps:');
  console.log('   1. Check Vercel function logs for detailed error information');
  console.log('   2. Verify backend URLs are accessible from Vercel servers');
  console.log('   3. Test individual backend health endpoints manually');
  
  return results;
}

// Validate URL
if (BASE_URL === 'https://your-deployment-url.vercel.app') {
  console.log('❌ Please provide your actual Vercel deployment URL:');
  console.log('   node test-deployment.js https://your-actual-url.vercel.app');
  process.exit(1);
}

runDeploymentTests().catch(console.error);
