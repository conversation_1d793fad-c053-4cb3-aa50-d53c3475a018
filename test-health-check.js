#!/usr/bin/env node

// Test script to verify health check functionality
const axios = require('axios');
const config = require('./config.json');

console.log('🏥 Testing Backend Health Check...\n');

async function testBackendHealth(backend) {
  console.log(`Testing ${backend.id} (${backend.url})...`);
  
  try {
    const healthUrl = `${backend.url}${backend.healthCheck.path}`;
    console.log(`   📡 Checking: ${healthUrl}`);
    
    const startTime = Date.now();
    const response = await axios.get(healthUrl, {
      timeout: backend.healthCheck.timeout,
      validateStatus: (status) => status >= 200 && status < 300,
      headers: {
        'User-Agent': 'BFF-HealthChecker/1.0',
        'Accept': 'application/json'
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    // Validate response data
    const responseData = response.data;
    const hasValidConfig = responseData && (
      typeof responseData === 'object' && 
      (responseData.data || responseData.config || Object.keys(responseData).length > 0)
    );
    
    if (hasValidConfig) {
      console.log(`   ✅ Status: ${response.status} ${response.statusText}`);
      console.log(`   ⏱️  Response Time: ${responseTime}ms`);
      console.log(`   📊 Config Keys: ${Object.keys(responseData).slice(0, 5).join(', ')}${Object.keys(responseData).length > 5 ? '...' : ''}`);
      console.log(`   💾 Data Size: ${JSON.stringify(responseData).length} bytes`);
      
      return {
        success: true,
        responseTime,
        status: response.status,
        dataSize: JSON.stringify(responseData).length,
        configKeys: Object.keys(responseData).length
      };
    } else {
      console.log(`   ❌ Invalid config response - empty or malformed data`);
      return {
        success: false,
        error: 'Invalid config response'
      };
    }
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    
    if (error.code === 'ENOTFOUND') {
      console.log(`   🌐 DNS resolution failed for ${backend.url}`);
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`   🔌 Connection refused by ${backend.url}`);
    } else if (error.code === 'ETIMEDOUT') {
      console.log(`   ⏰ Request timed out after ${backend.healthCheck.timeout}ms`);
    } else if (error.response) {
      console.log(`   📄 HTTP ${error.response.status}: ${error.response.statusText}`);
    }
    
    return {
      success: false,
      error: error.message,
      code: error.code
    };
  }
}

async function runHealthCheckTest() {
  console.log(`🔧 Configuration: ${config.backends.length} backends configured\n`);
  
  const results = [];
  
  for (const backend of config.backends) {
    const result = await testBackendHealth(backend);
    results.push({
      backend: backend.id,
      url: backend.url,
      ...result
    });
    console.log(''); // Empty line for readability
  }
  
  // Summary
  console.log('📋 Health Check Summary:');
  console.log('=' .repeat(50));
  
  const healthyBackends = results.filter(r => r.success);
  const unhealthyBackends = results.filter(r => !r.success);
  
  console.log(`✅ Healthy Backends: ${healthyBackends.length}/${results.length}`);
  console.log(`❌ Unhealthy Backends: ${unhealthyBackends.length}/${results.length}`);
  
  if (healthyBackends.length > 0) {
    console.log('\n🟢 Healthy Backends:');
    healthyBackends.forEach(backend => {
      console.log(`   • ${backend.backend}: ${backend.responseTime}ms, ${backend.configKeys} config keys`);
    });
  }
  
  if (unhealthyBackends.length > 0) {
    console.log('\n🔴 Unhealthy Backends:');
    unhealthyBackends.forEach(backend => {
      console.log(`   • ${backend.backend}: ${backend.error}`);
    });
  }
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  if (healthyBackends.length === 0) {
    console.log('   ⚠️  No backends are healthy! Check your backend URLs and network connectivity.');
  } else if (unhealthyBackends.length > 0) {
    console.log('   ⚠️  Some backends are unhealthy. The BFF will automatically route traffic to healthy backends.');
  } else {
    console.log('   🎉 All backends are healthy! The BFF is ready to handle traffic.');
  }
  
  console.log('\n🚀 Next Steps:');
  console.log('   1. Start the BFF server: npm start');
  console.log('   2. Monitor health status: http://localhost:3000/health');
  console.log('   3. View metrics: http://localhost:3000/metrics');
  
  return results;
}

// Handle command line execution
if (require.main === module) {
  runHealthCheckTest()
    .then(results => {
      const healthyCount = results.filter(r => r.success).length;
      process.exit(healthyCount > 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testBackendHealth, runHealthCheckTest };
