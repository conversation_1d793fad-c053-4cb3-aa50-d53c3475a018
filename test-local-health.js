#!/usr/bin/env node

const axios = require('axios');

async function testHealthEndpoint() {
  console.log('🔍 Testing local health endpoint...\n');
  
  try {
    const response = await axios.get('http://localhost:3000/health', {
      timeout: 10000,
      validateStatus: () => true // Accept any status code
    });
    
    console.log(`✅ Health endpoint responded with status: ${response.status}`);
    console.log('📄 Response headers:', response.headers);
    console.log('📊 Response data:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.status === 200) {
      console.log('\n🎉 Health endpoint is working correctly!');
    } else {
      console.log('\n⚠️  Health endpoint returned non-200 status');
    }
    
  } catch (error) {
    console.error('❌ Error testing health endpoint:');
    
    if (error.code === 'ECONNREFUSED') {
      console.error('   🔌 Connection refused - is the server running on port 3000?');
      console.error('   💡 Try running: npm start');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('   ⏰ Request timed out');
    } else if (error.response) {
      console.error(`   📄 HTTP ${error.response.status}: ${error.response.statusText}`);
      console.error('   📊 Response data:', error.response.data);
    } else {
      console.error('   🐛 Unexpected error:', error.message);
    }
  }
}

// Also test if server is running at all
async function testServerConnection() {
  console.log('🔍 Testing server connection...\n');
  
  try {
    const response = await axios.get('http://localhost:3000/', {
      timeout: 5000,
      validateStatus: () => true
    });
    
    console.log(`✅ Server is responding on port 3000 (status: ${response.status})`);
    return true;
  } catch (error) {
    console.error('❌ Server is not responding on port 3000');
    if (error.code === 'ECONNREFUSED') {
      console.error('   🔌 Connection refused - server is not running');
    }
    return false;
  }
}

async function main() {
  console.log('🧪 BFF Health Endpoint Test\n');
  
  const serverRunning = await testServerConnection();
  
  if (serverRunning) {
    console.log('');
    await testHealthEndpoint();
  } else {
    console.log('\n💡 To start the server, run: npm start');
  }
}

main().catch(console.error);
