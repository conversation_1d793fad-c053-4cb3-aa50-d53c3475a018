#!/usr/bin/env node

const axios = require('axios');

// Platform configurations
const platforms = {
  vercel: {
    name: 'Vercel',
    urlPattern: 'https://your-project.vercel.app',
    envVar: 'VERCEL_URL'
  },
  netlify: {
    name: 'Netlify',
    urlPattern: 'https://your-project.netlify.app',
    envVar: 'NETLIFY_URL'
  },
  cloudflare: {
    name: 'Cloudflare Workers',
    urlPattern: 'https://your-worker.your-subdomain.workers.dev',
    envVar: 'CLOUDFLARE_URL'
  },
  local: {
    name: 'Local Development',
    urlPattern: 'http://localhost:3000',
    envVar: 'LOCAL_URL'
  }
};

async function testPlatformEndpoint(baseUrl, endpoint, platformName) {
  const fullUrl = `${baseUrl}${endpoint}`;
  console.log(`   📡 Testing: ${fullUrl}`);
  
  try {
    const startTime = Date.now();
    const response = await axios.get(fullUrl, {
      timeout: 30000,
      validateStatus: () => true
    });
    const responseTime = Date.now() - startTime;
    
    console.log(`   ✅ Status: ${response.status} (${responseTime}ms)`);
    
    if (response.data) {
      if (typeof response.data === 'object') {
        console.log(`   📊 Response keys: ${Object.keys(response.data).slice(0, 5).join(', ')}`);
        
        // Platform-specific checks
        if (endpoint === '/health' && response.data.environment) {
          console.log(`   🌍 Environment: ${response.data.environment}`);
          
          if (response.data.backends) {
            const backendCount = Object.keys(response.data.backends).length;
            const healthyCount = Object.values(response.data.backends).filter(b => b.isHealthy).length;
            console.log(`   🎯 Backends: ${healthyCount}/${backendCount} healthy`);
          }
        }
        
        if (endpoint === '/status' && response.data.environment) {
          console.log(`   🌍 Environment: ${response.data.environment}`);
        }
      }
    }
    
    return {
      success: response.status >= 200 && response.status < 400,
      status: response.status,
      responseTime,
      environment: response.data?.environment
    };
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    
    if (error.code === 'ENOTFOUND') {
      console.log(`   🌐 DNS resolution failed`);
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`   🔌 Connection refused`);
    } else if (error.code === 'ETIMEDOUT') {
      console.log(`   ⏰ Request timed out`);
    }
    
    return {
      success: false,
      error: error.message,
      code: error.code
    };
  }
}

async function testPlatform(platformKey, baseUrl) {
  const platform = platforms[platformKey];
  console.log(`\n🚀 Testing ${platform.name}`);
  console.log(`   🌐 Base URL: ${baseUrl}`);
  
  const endpoints = ['/status', '/health', '/metrics'];
  const results = [];
  
  for (const endpoint of endpoints) {
    console.log(`\n   🧪 Testing ${endpoint}...`);
    const result = await testPlatformEndpoint(baseUrl, endpoint, platform.name);
    results.push({
      endpoint,
      ...result
    });
  }
  
  return {
    platform: platform.name,
    baseUrl,
    results
  };
}

async function runMultiPlatformTests() {
  console.log('🌍 Multi-Platform BFF Test Suite');
  console.log('═'.repeat(50));
  
  const testUrls = {};
  
  // Get URLs from command line arguments or environment variables
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // Parse command line arguments: platform=url
    args.forEach(arg => {
      const [platform, url] = arg.split('=');
      if (platforms[platform] && url) {
        testUrls[platform] = url;
      }
    });
  } else {
    // Try to get from environment variables
    Object.keys(platforms).forEach(key => {
      const envVar = platforms[key].envVar;
      if (process.env[envVar]) {
        testUrls[key] = process.env[envVar];
      }
    });
    
    // Default local URL if no others specified
    if (Object.keys(testUrls).length === 0) {
      testUrls.local = platforms.local.urlPattern;
    }
  }
  
  if (Object.keys(testUrls).length === 0) {
    console.log('❌ No platform URLs specified!');
    console.log('\n💡 Usage:');
    console.log('   node test-multi-platform.js vercel=https://your-project.vercel.app');
    console.log('   node test-multi-platform.js netlify=https://your-project.netlify.app');
    console.log('   node test-multi-platform.js cloudflare=https://your-worker.workers.dev');
    console.log('   node test-multi-platform.js local=http://localhost:3000');
    console.log('\n   Or set environment variables:');
    console.log('   VERCEL_URL=https://your-project.vercel.app');
    console.log('   NETLIFY_URL=https://your-project.netlify.app');
    console.log('   CLOUDFLARE_URL=https://your-worker.workers.dev');
    process.exit(1);
  }
  
  const allResults = [];
  
  for (const [platformKey, url] of Object.entries(testUrls)) {
    const results = await testPlatform(platformKey, url);
    allResults.push(results);
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('═'.repeat(50));
  
  allResults.forEach(platformResult => {
    console.log(`\n🚀 ${platformResult.platform}:`);
    
    const successful = platformResult.results.filter(r => r.success);
    const failed = platformResult.results.filter(r => !r.success);
    
    console.log(`   ✅ Successful: ${successful.length}/${platformResult.results.length}`);
    console.log(`   ❌ Failed: ${failed.length}/${platformResult.results.length}`);
    
    if (failed.length > 0) {
      console.log(`   🔍 Failed endpoints: ${failed.map(f => f.endpoint).join(', ')}`);
    }
    
    // Show environment detection
    const envResults = platformResult.results.filter(r => r.environment);
    if (envResults.length > 0) {
      console.log(`   🌍 Detected environment: ${envResults[0].environment}`);
    }
  });
  
  // Overall recommendations
  console.log('\n💡 Platform Comparison:');
  
  const healthyPlatforms = allResults.filter(pr => 
    pr.results.some(r => r.endpoint === '/health' && r.success)
  );
  
  if (healthyPlatforms.length === allResults.length) {
    console.log('   🎉 All platforms are working correctly!');
  } else {
    console.log('   ⚠️  Some platforms have issues. Check the logs above for details.');
  }
  
  console.log('\n🔗 Next Steps:');
  console.log('   1. Fix any failing platforms');
  console.log('   2. Verify environment detection is working correctly');
  console.log('   3. Test API proxy functionality');
  console.log('   4. Monitor performance across platforms');
  
  return allResults;
}

runMultiPlatformTests().catch(console.error);
