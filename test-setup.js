#!/usr/bin/env node

// Simple setup test script
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing BFF Setup...\n');

// Test 1: Check if all required files exist
const requiredFiles = [
  'package.json',
  'config.json',
  'server.js',
  'start.js',
  'src/healthChecker.js',
  'src/loadBalancer.js',
  'src/apiProxy.js',
  'src/performanceOptimizer.js',
  'src/monitoring.js',
  'src/logger.js'
];

console.log('📁 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`   ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing!');
  process.exit(1);
}

// Test 2: Check configuration
console.log('\n⚙️  Checking configuration...');
try {
  const config = JSON.parse(fs.readFileSync('config.json', 'utf8'));
  
  console.log(`   ✅ Config loaded successfully`);
  console.log(`   ✅ Backends configured: ${config.backends?.length || 0}`);
  console.log(`   ✅ Server port: ${config.server?.port || 3000}`);
  console.log(`   ✅ Load balancer strategy: ${config.loadBalancer?.strategy || 'weighted-round-robin'}`);
  
  // Validate backends
  if (!config.backends || config.backends.length === 0) {
    console.log('   ⚠️  No backends configured');
  } else {
    config.backends.forEach((backend, index) => {
      if (backend.id && backend.url) {
        console.log(`   ✅ Backend ${index + 1}: ${backend.id} (${backend.url})`);
      } else {
        console.log(`   ❌ Backend ${index + 1}: Missing id or url`);
      }
    });
  }
  
} catch (error) {
  console.log(`   ❌ Config error: ${error.message}`);
  process.exit(1);
}

// Test 3: Check Node.js modules can be loaded
console.log('\n📦 Testing module loading...');
try {
  const HealthChecker = require('./src/healthChecker');
  console.log('   ✅ HealthChecker module loaded');
  
  const LoadBalancer = require('./src/loadBalancer');
  console.log('   ✅ LoadBalancer module loaded');
  
  const ApiProxy = require('./src/apiProxy');
  console.log('   ✅ ApiProxy module loaded');
  
  const PerformanceOptimizer = require('./src/performanceOptimizer');
  console.log('   ✅ PerformanceOptimizer module loaded');
  
  const MonitoringService = require('./src/monitoring');
  console.log('   ✅ MonitoringService module loaded');
  
  const logger = require('./src/logger');
  console.log('   ✅ Logger module loaded');
  
  const BFFServer = require('./server');
  console.log('   ✅ BFFServer module loaded');
  
} catch (error) {
  console.log(`   ❌ Module loading error: ${error.message}`);
  console.log('\n💡 You may need to run "npm install" to install dependencies.');
  process.exit(1);
}

// Test 4: Create logs directory
console.log('\n📁 Setting up directories...');
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('   ✅ Created logs directory');
} else {
  console.log('   ✅ Logs directory exists');
}

console.log('\n🎉 Setup test completed successfully!');
console.log('\n🚀 Next steps:');
console.log('   1. Run "npm install" to install dependencies');
console.log('   2. Run "npm run test-health" to test backend connectivity');
console.log('   3. Run "npm start" or "node start.js" to start the server');
console.log('   4. Run "npm test" to run the test suite');
console.log('   5. Visit http://localhost:3000/health to check server status');

console.log('\n📖 For more information, see README.md');
