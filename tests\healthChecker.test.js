const HealthChecker = require('../src/healthChecker');
const axios = require('axios');

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock node-cron
jest.mock('node-cron', () => ({
  schedule: jest.fn(() => ({
    start: jest.fn(),
    stop: jest.fn()
  }))
}));

describe('HealthChecker', () => {
  let healthChecker;
  let mockConfig;

  beforeEach(() => {
    mockConfig = {
      backends: [
        {
          id: 'backend1',
          url: 'https://api.example.com',
          priority: 1,
          weight: 100,
          healthCheck: {
            enabled: true,
            path: '/health',
            interval: 30000,
            timeout: 3000,
            retries: 2
          }
        },
        {
          id: 'backend2',
          url: 'https://api2.example.com',
          priority: 2,
          weight: 80,
          healthCheck: {
            enabled: true,
            path: '/health',
            interval: 30000,
            timeout: 3000,
            retries: 2
          }
        }
      ],
      loadBalancer: {
        circuitBreaker: {
          failureThreshold: 5,
          resetTimeout: 60000
        }
      }
    };

    healthChecker = new HealthChecker(mockConfig);
  });

  afterEach(() => {
    healthChecker.stop();
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    test('should initialize backends correctly', () => {
      const backend1 = healthChecker.getBackendStatus('backend1');
      const backend2 = healthChecker.getBackendStatus('backend2');

      expect(backend1).toBeDefined();
      expect(backend1.id).toBe('backend1');
      expect(backend1.status).toBe('unknown');
      expect(backend1.isHealthy).toBe(false);

      expect(backend2).toBeDefined();
      expect(backend2.id).toBe('backend2');
      expect(backend2.status).toBe('unknown');
      expect(backend2.isHealthy).toBe(false);
    });

    test('should initialize circuit breakers', () => {
      const allStatus = healthChecker.getAllBackendStatus();
      
      expect(allStatus.backend1.circuitBreaker).toBeDefined();
      expect(allStatus.backend1.circuitBreaker.state).toBe('closed');
      expect(allStatus.backend1.circuitBreaker.failures).toBe(0);

      expect(allStatus.backend2.circuitBreaker).toBeDefined();
      expect(allStatus.backend2.circuitBreaker.state).toBe('closed');
      expect(allStatus.backend2.circuitBreaker.failures).toBe(0);
    });
  });

  describe('health checking', () => {
    test('should mark backend as healthy on successful check', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: { status: 'ok' }
      });

      await healthChecker.checkBackendHealth('backend1');

      const backend = healthChecker.getBackendStatus('backend1');
      expect(backend.isHealthy).toBe(true);
      expect(backend.status).toBe('healthy');
      expect(backend.consecutiveFailures).toBe(0);
      expect(backend.responseTime).toBeGreaterThan(0);
    });

    test('should mark backend as unhealthy on failed check', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Connection failed'));

      await healthChecker.checkBackendHealth('backend1');

      const backend = healthChecker.getBackendStatus('backend1');
      expect(backend.isHealthy).toBe(false);
      expect(backend.status).toBe('unhealthy');
      expect(backend.consecutiveFailures).toBe(1);
      expect(backend.responseTime).toBeNull();
    });

    test('should emit healthChange event on status change', async () => {
      const healthChangeHandler = jest.fn();
      healthChecker.on('healthChange', healthChangeHandler);

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: { status: 'ok' }
      });

      await healthChecker.checkBackendHealth('backend1');

      expect(healthChangeHandler).toHaveBeenCalledWith({
        backendId: 'backend1',
        isHealthy: true,
        backend: expect.objectContaining({
          id: 'backend1',
          isHealthy: true,
          status: 'healthy'
        })
      });
    });
  });

  describe('circuit breaker', () => {
    test('should open circuit breaker after threshold failures', async () => {
      // Mock multiple failures
      mockedAxios.get.mockRejectedValue(new Error('Connection failed'));

      // Trigger failures to reach threshold
      for (let i = 0; i < 5; i++) {
        await healthChecker.checkBackendHealth('backend1');
      }

      const status = healthChecker.getAllBackendStatus();
      expect(status.backend1.circuitBreaker.state).toBe('open');
    });

    test('should reset circuit breaker on successful check after half-open', async () => {
      // First, open the circuit breaker
      mockedAxios.get.mockRejectedValue(new Error('Connection failed'));
      for (let i = 0; i < 5; i++) {
        await healthChecker.checkBackendHealth('backend1');
      }

      // Manually set to half-open for testing
      const circuitBreaker = healthChecker.circuitBreakers.get('backend1');
      circuitBreaker.state = 'half-open';

      // Mock successful response
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: { status: 'ok' }
      });

      await healthChecker.checkBackendHealth('backend1');

      const status = healthChecker.getAllBackendStatus();
      expect(status.backend1.circuitBreaker.state).toBe('closed');
      expect(status.backend1.circuitBreaker.failures).toBe(0);
    });
  });

  describe('utility methods', () => {
    test('should return only healthy backends', async () => {
      // Make backend1 healthy
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: { status: 'ok' }
      });
      await healthChecker.checkBackendHealth('backend1');

      // Make backend2 unhealthy
      mockedAxios.get.mockRejectedValueOnce(new Error('Connection failed'));
      await healthChecker.checkBackendHealth('backend2');

      const healthyBackends = healthChecker.getHealthyBackends();
      expect(healthyBackends).toHaveLength(1);
      expect(healthyBackends[0].id).toBe('backend1');
    });

    test('should return all backend status', () => {
      const allStatus = healthChecker.getAllBackendStatus();
      
      expect(Object.keys(allStatus)).toHaveLength(2);
      expect(allStatus.backend1).toBeDefined();
      expect(allStatus.backend2).toBeDefined();
      
      expect(allStatus.backend1).toHaveProperty('id');
      expect(allStatus.backend1).toHaveProperty('url');
      expect(allStatus.backend1).toHaveProperty('status');
      expect(allStatus.backend1).toHaveProperty('isHealthy');
      expect(allStatus.backend1).toHaveProperty('circuitBreaker');
    });
  });

  describe('interval conversion', () => {
    test('should convert milliseconds to cron expression correctly', () => {
      // Test seconds
      expect(healthChecker.intervalToCron(30000)).toBe('*/30 * * * * *');
      
      // Test minutes
      expect(healthChecker.intervalToCron(120000)).toBe('*/2 * * * *');
    });
  });
});
