const LoadBalancer = require('../src/loadBalancer');
const HealthChecker = require('../src/healthChecker');

// Mock HealthChecker
jest.mock('../src/healthChecker');

describe('LoadBalancer', () => {
  let loadBalancer;
  let mockHealthChecker;
  let mockConfig;
  let mockBackends;

  beforeEach(() => {
    mockBackends = [
      {
        id: 'backend1',
        url: 'https://api1.example.com',
        priority: 1,
        weight: 100
      },
      {
        id: 'backend2',
        url: 'https://api2.example.com',
        priority: 2,
        weight: 80
      },
      {
        id: 'backend3',
        url: 'https://api3.example.com',
        priority: 1,
        weight: 60
      }
    ];

    mockConfig = {
      loadBalancer: {
        strategy: 'weighted-round-robin'
      }
    };

    mockHealthChecker = {
      getHealthyBackends: jest.fn().mockReturnValue(mockBackends),
      on: jest.fn()
    };

    loadBalancer = new LoadBalancer(mockConfig, mockHealthChecker);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    test('should initialize with healthy backends', () => {
      expect(mockHealthChecker.on).toHaveBeenCalledWith('healthChange', expect.any(Function));
      expect(loadBalancer.weightedList.length).toBeGreaterThan(0);
    });

    test('should create weighted list based on backend weights', () => {
      // backend1: weight 100, backend2: weight 80, backend3: weight 60
      // Total entries should be 100 + 80 + 60 = 240
      expect(loadBalancer.weightedList.length).toBe(240);
      
      // Count occurrences of each backend
      const backend1Count = loadBalancer.weightedList.filter(b => b.id === 'backend1').length;
      const backend2Count = loadBalancer.weightedList.filter(b => b.id === 'backend2').length;
      const backend3Count = loadBalancer.weightedList.filter(b => b.id === 'backend3').length;
      
      expect(backend1Count).toBe(100);
      expect(backend2Count).toBe(80);
      expect(backend3Count).toBe(60);
    });
  });

  describe('backend selection strategies', () => {
    test('should select backend using round-robin', () => {
      const backend1 = loadBalancer.selectBackend('round-robin');
      const backend2 = loadBalancer.selectBackend('round-robin');
      const backend3 = loadBalancer.selectBackend('round-robin');
      const backend4 = loadBalancer.selectBackend('round-robin'); // Should wrap around

      expect(backend1.id).toBe('backend1');
      expect(backend2.id).toBe('backend2');
      expect(backend3.id).toBe('backend3');
      expect(backend4.id).toBe('backend1'); // Wrapped around
    });

    test('should select backend using weighted round-robin', () => {
      const selections = [];
      
      // Make multiple selections to test weight distribution
      for (let i = 0; i < 240; i++) {
        const backend = loadBalancer.selectBackend('weighted-round-robin');
        selections.push(backend.id);
      }

      // Count selections for each backend
      const backend1Count = selections.filter(id => id === 'backend1').length;
      const backend2Count = selections.filter(id => id === 'backend2').length;
      const backend3Count = selections.filter(id => id === 'backend3').length;

      expect(backend1Count).toBe(100);
      expect(backend2Count).toBe(80);
      expect(backend3Count).toBe(60);
    });

    test('should select backend using priority strategy', () => {
      const backend = loadBalancer.selectBackend('priority');
      
      // Should select backend with highest priority (lowest priority number)
      // Both backend1 and backend3 have priority 1, but backend1 comes first
      expect(backend.priority).toBe(1);
    });

    test('should select backend using least connections', () => {
      // Simulate some connections
      loadBalancer.requestCounts.set('backend1', 5);
      loadBalancer.requestCounts.set('backend2', 3);
      loadBalancer.requestCounts.set('backend3', 7);

      const backend = loadBalancer.selectBackend('least-connections');
      
      // Should select backend2 with least connections (3)
      expect(backend.id).toBe('backend2');
    });

    test('should select backend using random strategy', () => {
      const backend = loadBalancer.selectBackend('random');
      
      expect(backend).toBeDefined();
      expect(['backend1', 'backend2', 'backend3']).toContain(backend.id);
    });

    test('should fall back to weighted round-robin for unknown strategy', () => {
      const backend = loadBalancer.selectBackend('unknown-strategy');
      
      expect(backend).toBeDefined();
      expect(['backend1', 'backend2', 'backend3']).toContain(backend.id);
    });
  });

  describe('failover support', () => {
    test('should exclude failed backends in failover selection', async () => {
      const excludeBackends = ['backend1', 'backend2'];
      
      const backend = await loadBalancer.selectBackendWithFailover(excludeBackends);
      
      expect(backend.id).toBe('backend3');
    });

    test('should return null when no healthy backends available', async () => {
      mockHealthChecker.getHealthyBackends.mockReturnValue([]);
      
      const backend = await loadBalancer.selectBackendWithFailover();
      
      expect(backend).toBeNull();
    });

    test('should return null when all backends are excluded', async () => {
      const excludeBackends = ['backend1', 'backend2', 'backend3'];
      
      const backend = await loadBalancer.selectBackendWithFailover(excludeBackends);
      
      expect(backend).toBeNull();
    });
  });

  describe('connection management', () => {
    test('should increment request count on selection', () => {
      const backend = loadBalancer.selectBackend();
      
      expect(loadBalancer.requestCounts.get(backend.id)).toBe(1);
    });

    test('should decrement request count on release', () => {
      const backend = loadBalancer.selectBackend();
      const backendId = backend.id;
      
      expect(loadBalancer.requestCounts.get(backendId)).toBe(1);
      
      loadBalancer.releaseConnection(backendId);
      
      expect(loadBalancer.requestCounts.get(backendId)).toBe(0);
    });

    test('should not go below zero on release', () => {
      loadBalancer.releaseConnection('backend1');
      
      expect(loadBalancer.requestCounts.get('backend1')).toBe(0);
    });
  });

  describe('statistics', () => {
    test('should return load balancer statistics', () => {
      // Make some selections
      loadBalancer.selectBackend();
      loadBalancer.selectBackend();
      loadBalancer.selectBackend();

      const stats = loadBalancer.getStats();
      
      expect(stats).toHaveProperty('strategy');
      expect(stats).toHaveProperty('totalHealthyBackends');
      expect(stats).toHaveProperty('totalRequests');
      expect(stats).toHaveProperty('requestDistribution');
      expect(stats).toHaveProperty('backends');
      
      expect(stats.strategy).toBe('weighted-round-robin');
      expect(stats.totalHealthyBackends).toBe(3);
      expect(stats.totalRequests).toBe(3);
      expect(stats.backends).toHaveLength(3);
    });

    test('should reset statistics', () => {
      // Make some selections
      loadBalancer.selectBackend();
      loadBalancer.selectBackend();
      
      loadBalancer.resetStats();
      
      const stats = loadBalancer.getStats();
      expect(stats.totalRequests).toBe(0);
      expect(loadBalancer.currentIndex).toBe(0);
    });
  });

  describe('health change handling', () => {
    test('should update weighted list on health change', () => {
      const originalLength = loadBalancer.weightedList.length;
      
      // Simulate health change with fewer backends
      mockHealthChecker.getHealthyBackends.mockReturnValue([mockBackends[0]]);
      
      // Trigger health change event
      const healthChangeCallback = mockHealthChecker.on.mock.calls[0][1];
      healthChangeCallback();
      
      expect(loadBalancer.weightedList.length).toBeLessThan(originalLength);
      expect(loadBalancer.weightedList.length).toBe(100); // Only backend1's weight
    });
  });

  describe('edge cases', () => {
    test('should handle empty healthy backends list', () => {
      mockHealthChecker.getHealthyBackends.mockReturnValue([]);
      loadBalancer.initializeWeightedList();
      
      const backend = loadBalancer.selectBackend();
      
      expect(backend).toBeNull();
      expect(loadBalancer.weightedList.length).toBe(0);
    });

    test('should handle backends with zero weight', () => {
      const backendsWithZeroWeight = [
        { ...mockBackends[0], weight: 0 },
        { ...mockBackends[1], weight: 50 }
      ];
      
      mockHealthChecker.getHealthyBackends.mockReturnValue(backendsWithZeroWeight);
      loadBalancer.initializeWeightedList();
      
      expect(loadBalancer.weightedList.length).toBe(50); // Only backend2's weight
    });
  });
});
