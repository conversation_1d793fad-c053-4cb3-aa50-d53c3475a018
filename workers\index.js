// Cloudflare Workers entry point
// Note: This is a simplified adapter. For production use, consider using a proper framework adapter.

// Import the BFF server (you may need to adjust the import based on your setup)
// const { BFFServer } = require('../api/server.js');

// Cloudflare Workers fetch handler
export default {
  async fetch(request, env, ctx) {
    // Set Cloudflare Workers environment variables
    globalThis.process = globalThis.process || {};
    globalThis.process.env = globalThis.process.env || {};
    globalThis.process.env.CLOUDFLARE_WORKERS = 'true';
    globalThis.process.env.NODE_ENV = env.NODE_ENV || 'production';
    
    try {
      // Convert Cloudflare Workers request to Express-compatible format
      const response = await handleRequest(request, env, ctx);
      return response;
    } catch (error) {
      console.error('Cloudflare Workers error:', error);
      
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error.message,
        timestamp: new Date().toISOString(),
        environment: 'cloudflare-workers'
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
};

// Convert Cloudflare Workers request to Express-compatible format
async function handleRequest(request, env, ctx) {
  const url = new URL(request.url);
  const method = request.method;
  
  // Handle CORS preflight requests
  if (method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400'
      }
    });
  }
  
  // Create Express-compatible request object
  const expressReq = {
    method,
    url: url.pathname + url.search,
    headers: Object.fromEntries(request.headers.entries()),
    body: method !== 'GET' && method !== 'HEAD' ? await request.text() : undefined,
    ip: request.headers.get('CF-Connecting-IP') || '127.0.0.1',
    get: function(header) {
      return this.headers[header.toLowerCase()];
    }
  };
  
  // Create Express-compatible response object
  let responseStatus = 200;
  let responseHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  };
  let responseBody = '';
  
  const expressRes = {
    statusCode: 200,
    headers: responseHeaders,
    status: function(code) {
      responseStatus = code;
      this.statusCode = code;
      return this;
    },
    json: function(data) {
      responseBody = JSON.stringify(data);
      responseHeaders['Content-Type'] = 'application/json';
      return this;
    },
    send: function(data) {
      responseBody = data;
      return this;
    },
    set: function(header, value) {
      responseHeaders[header] = value;
      return this;
    },
    get: function(header) {
      return responseHeaders[header];
    }
  };
  
  // Route the request through the BFF server
  await routeRequest(expressReq, expressRes, url.pathname);
  
  // Convert back to Cloudflare Workers Response
  return new Response(responseBody, {
    status: responseStatus,
    headers: responseHeaders
  });
}

// Route requests to appropriate handlers
async function routeRequest(req, res, pathname) {
  try {
    // Health check endpoint
    if (pathname === '/health') {
      await bffServer.handleHealthCheck(req, res);
    }
    // Status endpoint
    else if (pathname === '/status') {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: Date.now() / 1000, // Approximate uptime
        environment: 'cloudflare-workers',
        version: '1.0.0' // You might want to read this from package.json
      });
    }
    // Metrics endpoint
    else if (pathname === '/metrics') {
      await bffServer.handleMetrics(req, res);
    }
    // Alerts endpoint
    else if (pathname === '/alerts') {
      await bffServer.handleAlerts(req, res);
    }
    // API proxy
    else if (pathname.startsWith('/api/')) {
      await bffServer.handleApiProxy(req, res);
    }
    // 404 for other routes
    else {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${pathname} not found`,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Route handling error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
