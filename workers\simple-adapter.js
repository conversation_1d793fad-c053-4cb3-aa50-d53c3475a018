// Simplified Cloudflare Workers adapter for BFF
// This is a basic implementation - for production, consider using itty-router or similar

// Mock Node.js globals for Cloudflare Workers
globalThis.process = globalThis.process || {
  env: {},
  version: 'v18.0.0',
  platform: 'cloudflare',
  uptime: () => Date.now() / 1000,
  memoryUsage: () => ({ rss: 0, heapTotal: 0, heapUsed: 0, external: 0 })
};

// Set Cloudflare Workers environment
globalThis.process.env.CLOUDFLARE_WORKERS = 'true';
globalThis.process.env.NODE_ENV = 'production';

// Basic health check data
const healthData = {
  backends: {
    'api-fast-ai': {
      id: 'api-fast-ai',
      url: 'https://api.fast-ai.xyz',
      status: 'unknown',
      isHealthy: false,
      lastCheck: null,
      responseTime: null,
      consecutiveFailures: 0
    },
    'get-fast-ai': {
      id: 'get-fast-ai',
      url: 'https://get.fast-ai.xyz',
      status: 'unknown',
      isHealthy: false,
      lastCheck: null,
      responseTime: null,
      consecutiveFailures: 0
    }
  }
};

// Simple backend health check
async function checkBackendHealth(backend) {
  try {
    const healthUrl = `${backend.url}/api/v1/guest/comm/config`;
    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'BFF-HealthChecker/1.0',
        'Accept': 'application/json'
      },
      signal: AbortSignal.timeout(15000) // 15 second timeout
    });

    if (response.ok) {
      const data = await response.json();
      const hasValidConfig = data && typeof data === 'object' && Object.keys(data).length > 0;
      
      if (hasValidConfig) {
        backend.status = 'healthy';
        backend.isHealthy = true;
        backend.consecutiveFailures = 0;
        backend.responseTime = Date.now() - startTime;
      } else {
        throw new Error('Invalid config response');
      }
    } else {
      throw new Error(`HTTP ${response.status}`);
    }
  } catch (error) {
    backend.status = 'unhealthy';
    backend.isHealthy = false;
    backend.consecutiveFailures++;
    backend.responseTime = null;
  }
  
  backend.lastCheck = new Date().toISOString();
  return backend;
}

// Main fetch handler
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // CORS headers
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    };

    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 200, headers: corsHeaders });
    }

    try {
      // Route handling
      if (pathname === '/health') {
        return await handleHealth(corsHeaders);
      } else if (pathname === '/status') {
        return handleStatus(corsHeaders);
      } else if (pathname === '/metrics') {
        return handleMetrics(corsHeaders);
      } else if (pathname.startsWith('/api/')) {
        return await handleApiProxy(request, corsHeaders);
      } else {
        return new Response(JSON.stringify({
          error: 'Not Found',
          message: `Route ${request.method} ${pathname} not found`,
          timestamp: new Date().toISOString()
        }), {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: error.message,
        timestamp: new Date().toISOString(),
        environment: 'cloudflare-workers'
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  }
};

// Health check handler
async function handleHealth(corsHeaders) {
  // Perform health checks
  const backends = Object.values(healthData.backends);
  await Promise.allSettled(backends.map(backend => checkBackendHealth(backend)));
  
  const overallHealth = backends.some(backend => backend.isHealthy);
  
  const response = {
    status: overallHealth ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    backends: healthData.backends,
    loadBalancer: { strategy: 'weighted-round-robin' },
    cache: { enabled: true, size: 0 },
    memory: process.memoryUsage(),
    version: '1.0.0',
    environment: 'cloudflare-workers',
    note: 'Health checks performed on-demand in serverless environment'
  };

  return new Response(JSON.stringify(response), {
    status: overallHealth ? 200 : 503,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// Status handler
function handleStatus(corsHeaders) {
  return new Response(JSON.stringify({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: 'cloudflare-workers',
    version: '1.0.0'
  }), {
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// Metrics handler
function handleMetrics(corsHeaders) {
  return new Response(JSON.stringify({
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    backends: healthData.backends,
    system: {
      memory: process.memoryUsage(),
      platform: process.platform,
      nodeVersion: process.version
    },
    environment: 'cloudflare-workers'
  }), {
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// API proxy handler (simplified)
async function handleApiProxy(request, corsHeaders) {
  // Simple load balancing - pick first healthy backend
  const healthyBackend = Object.values(healthData.backends).find(b => b.isHealthy);
  
  if (!healthyBackend) {
    return new Response(JSON.stringify({
      error: true,
      message: 'No healthy backends available',
      timestamp: new Date().toISOString(),
      responseTime: '0ms'
    }), {
      status: 503,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // Proxy the request
  const url = new URL(request.url);
  const targetUrl = `${healthyBackend.url}${url.pathname}${url.search}`;
  
  try {
    const response = await fetch(targetUrl, {
      method: request.method,
      headers: request.headers,
      body: request.method !== 'GET' && request.method !== 'HEAD' ? request.body : undefined
    });

    return new Response(response.body, {
      status: response.status,
      headers: { ...corsHeaders, ...Object.fromEntries(response.headers.entries()) }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      error: true,
      message: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}
