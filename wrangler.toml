name = "high-speed-bff"
main = "workers/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Environment variables
[vars]
NODE_ENV = "production"
CLOUDFLARE_WORKERS = "true"
LOG_LEVEL = "info"
HEALTH_CHECK_TIMEOUT_OVERRIDE = "15000"

# KV namespaces for caching (optional)
# [[kv_namespaces]]
# binding = "BFF_CACHE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

# Durable Objects (if needed for advanced features)
# [[durable_objects.bindings]]
# name = "BFF_STATE"
# class_name = "BFFState"

# Routes for custom domain (optional)
# [[routes]]
# pattern = "api.yourdomain.com/*"
# zone_name = "yourdomain.com"

# Build configuration
[build]
command = "npm run build:workers"

# Development configuration
[env.development]
name = "high-speed-bff-dev"

[env.development.vars]
NODE_ENV = "development"
LOG_LEVEL = "debug"

# Production configuration
[env.production]
name = "high-speed-bff-prod"

[env.production.vars]
NODE_ENV = "production"
LOG_LEVEL = "info"

# Limits and performance
[limits]
cpu_ms = 50000  # 50 seconds CPU time limit
